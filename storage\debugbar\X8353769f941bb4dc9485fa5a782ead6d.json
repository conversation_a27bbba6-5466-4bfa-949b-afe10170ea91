{"__meta": {"id": "X8353769f941bb4dc9485fa5a782ead6d", "datetime": "2025-07-01 18:22:22", "utime": 1751383342.855735, "method": "POST", "uri": "/livewire/message/multi-step-register", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751383335.043335, "end": 1751383342.855758, "duration": 7.81242299079895, "duration_str": "7.81s", "measures": [{"label": "Booting", "start": 1751383335.043335, "relative_start": 0, "end": 1751383335.463423, "relative_end": 1751383335.463423, "duration": 0.4200880527496338, "duration_str": "420ms", "params": [], "collector": null}, {"label": "Application", "start": 1751383335.463972, "relative_start": 0.4206371307373047, "end": 1751383342.85576, "relative_end": 2.1457672119140625e-06, "duration": 7.391788005828857, "duration_str": "7.39s", "params": [], "collector": null}]}, "memory": {"peak_usage": 36634616, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 11, "templates": [{"name": "emails.inscription (\\resources\\views\\emails\\inscription.blade.php)", "param_count": 11, "params": ["nom", "prenom", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/emails/inscription.blade.php&line=0"}, {"name": "mail::message (\\resources\\views\\vendor\\mail\\html\\message.blade.php)", "param_count": 2, "params": ["slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/html/message.blade.php&line=0"}, {"name": "mail::header (\\resources\\views\\vendor\\mail\\html\\header.blade.php)", "param_count": 4, "params": ["url", "attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/html/header.blade.php&line=0"}, {"name": "mail::footer (\\resources\\views\\vendor\\mail\\html\\footer.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/html/footer.blade.php&line=0"}, {"name": "mail::layout (\\resources\\views\\vendor\\mail\\html\\layout.blade.php)", "param_count": 5, "params": ["attributes", "slot", "header", "footer", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/html/layout.blade.php&line=0"}, {"name": "mail::themes.default (\\resources\\views\\vendor\\mail\\html\\themes\\default.css)", "param_count": 11, "params": ["nom", "prenom", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained"], "type": "css", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/html/themes/default.css&line=0"}, {"name": "emails.inscription (\\resources\\views\\emails\\inscription.blade.php)", "param_count": 11, "params": ["nom", "prenom", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/emails/inscription.blade.php&line=0"}, {"name": "mail::message (\\resources\\views\\vendor\\mail\\text\\message.blade.php)", "param_count": 2, "params": ["slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/text/message.blade.php&line=0"}, {"name": "mail::header (\\resources\\views\\vendor\\mail\\text\\header.blade.php)", "param_count": 4, "params": ["url", "attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/text/header.blade.php&line=0"}, {"name": "mail::footer (\\resources\\views\\vendor\\mail\\text\\footer.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/text/footer.blade.php&line=0"}, {"name": "mail::layout (\\resources\\views\\vendor\\mail\\text\\layout.blade.php)", "param_count": 5, "params": ["attributes", "slot", "header", "footer", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/vendor/mail/text/layout.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.03579, "accumulated_duration_str": "35.79ms", "statements": [{"sql": "select * from `mentions` where `mentions`.`id` in (1, 2, 3, 4, 5, 6, 7, 11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.004070000000000001, "duration_str": "4.07ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 11.372}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 904}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 611}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 417}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 448}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:54", "connection": "inscriptionimsaa", "start_percent": 11.372, "width_percent": 2.571}, {"sql": "select count(*) as aggregate from `users` where `cin` = '123412341234'", "type": "query", "params": [], "bindings": ["123412341234"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 904}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 611}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 417}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 448}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:54", "connection": "inscriptionimsaa", "start_percent": 13.942, "width_percent": 2.012}, {"sql": "insert into `users` (`nom`, `prenom`, `email`, `sexe`, `date_naissance`, `lieu_naissance`, `nationalite`, `adresse`, `telephone1`, `cin`, `reference`, `telenvoi`, `montantenvoi`, `photo`, `mention_id`, `niveau_id`, `updated_at`, `created_at`) values ('qdfqs', 'qsdfqsdf', '<EMAIL>', 'H', '2002-01-03', 'qsdf', 'qsdf', 'qsdf', '+261 32 55 516 50', '123412341234', '12341', '123412', '123421', 'media/avatars/avatar0.jpg', 1, '1', '2025-07-01 18:22:15', '2025-07-01 18:22:15')", "type": "query", "params": [], "bindings": ["qdfqs", "qsdfqsdf", "<EMAIL>", "H", "2002-01-03", "qsdf", "qsdf", "qsdf", "+261 32 55 516 50", "123412341234", "12341", "123412", "123421", "media/avatars/avatar0.jpg", "1", "1", "2025-07-01 18:22:15", "2025-07-01 18:22:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\MultiStepRegister.php", "line": 366}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01401, "duration_str": "14.01ms", "stmt_id": "\\app\\Http\\Livewire\\MultiStepRegister.php:366", "connection": "inscriptionimsaa", "start_percent": 15.954, "width_percent": 39.145}, {"sql": "insert into `role_user` (`role_id`, `user_id`) values (5, 556)", "type": "query", "params": [], "bindings": ["5", "556"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Livewire\\MultiStepRegister.php", "line": 367}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00513, "duration_str": "5.13ms", "stmt_id": "\\app\\Http\\Livewire\\MultiStepRegister.php:367", "connection": "inscriptionimsaa", "start_percent": 55.099, "width_percent": 14.334}, {"sql": "select max(`order_column`) as aggregate from `media` where `model_type` = 'App\\Models\\User' and `model_id` = 556", "type": "query", "params": [], "bindings": ["App\\Models\\User", "556"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Concerns\\IsSorted.php", "line": 20}, {"index": 16, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Concerns\\IsSorted.php", "line": 13}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Observers\\MediaObserver.php", "line": 15}, {"index": 25, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 440}, {"index": 26, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 427}], "duration": 0.00271, "duration_str": "2.71ms", "stmt_id": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Concerns\\IsSorted.php:20", "connection": "inscriptionimsaa", "start_percent": 69.433, "width_percent": 7.572}, {"sql": "insert into `media` (`name`, `file_name`, `disk`, `conversions_disk`, `collection_name`, `mime_type`, `size`, `custom_properties`, `generated_conversions`, `responsive_images`, `manipulations`, `model_id`, `model_type`, `uuid`, `order_column`, `updated_at`, `created_at`) values ('2', '2.png', 'private', 'private', 'Releve_ou_Diplome_Bacc', 'image/png', 93891, '[]', '[]', '[]', '[]', 556, 'App\\Models\\User', '550e3ae8-7fc9-4822-8683-b8adb03a9f34', 1, '2025-07-01 18:22:15', '2025-07-01 18:22:15')", "type": "query", "params": [], "bindings": ["2", "2.png", "private", "private", "<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>me_Bacc", "image/png", "93891", "[]", "[]", "[]", "[]", "556", "App\\Models\\User", "550e3ae8-7fc9-4822-8683-b8adb03a9f34", "1", "2025-07-01 18:22:15", "2025-07-01 18:22:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 440}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 427}, {"index": 18, "namespace": null, "name": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 346}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\MultiStepRegister.php", "line": 369}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.008230000000000001, "duration_str": "8.23ms", "stmt_id": "\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php:440", "connection": "inscriptionimsaa", "start_percent": 77.005, "width_percent": 22.995}]}, "models": {"data": {"App\\Models\\Mention": 9}, "count": 9}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png?expires=1751385088&signature=61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/livewire/message/multi-step-register", "status_code": "<pre class=sf-dump id=sf-dump-1925293251 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1925293251\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-491667029 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-491667029\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1139805617 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">jEC3nyDduoNea0HbQ9Py</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">multi-step-register</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">d989355b</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">qdfqs</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">qsdfqsdf</span>\"\n        \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2002-01-03</span>\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n        \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n        \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n        \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n        \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n        \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12341</span>\"\n        \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123412</span>\"\n        \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123421</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>parcours</span>\" => []\n      \"<span class=sf-dump-key>cv</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>diplome</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>relevebacc</span>\" => \"<span class=sf-dump-str title=\"62 characters\">livewire-file:8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png</span>\"\n      \"<span class=sf-dump-key>releve1</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve2</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve3</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve4</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>terms</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>iteration</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"8 characters\">paiement</span>\"\n      \"<span class=sf-dump-key>previewDocument</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcours</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Mention</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>4</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>5</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>6</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>7</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>11</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>8</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">78f295fdc535d8550a2640c20d85072554ff2fa5effbdb25fd9c2956b75e6d13</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">wf0z</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">register</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139805617\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1483672856 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1099</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkdwaUptYldUSmF6WnMxUDM4em9YQ0E9PSIsInZhbHVlIjoieVJBS3djaVhhVFZmdFhvbkQ3ZXVyZEJWQm5DeFZRNjk4TzJicE1yak5MVUc3bEF5WmtjM2RaZmI0ci8zbFpVa0tBV2hDdDlpcC9rL1dyaUtKc0VsaW5jRStRWmxBMmcyK2ZvODR3STI5aCsvcWxMdkIzb2lFODNWSTEwM2FSWTMiLCJtYWMiOiIzZTdiY2QwYjZlODYyMTJjMThhNThkNDRmNzE1MDUyMjA1NDU2M2Y1N2JiMTcyN2FmNmRjZGFhZjQ0MTZhOWZjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkxWd2ZTWHdQRzF3UkVSMUVyS0REZmc9PSIsInZhbHVlIjoiUFdEMGJQM1NySCt1R3NJK1ArdFgrTnFOY3FCdlh3QmJNYVBrMGE0QWFuL3EvbnBFRFpab1lnWEQrdCtzWGdKc3RYUVlvd2tLUVNZWGVCS2tOc2NNM1B5blFkSUtBb3JEZmZ3dkh2VlhUZzZsdlBXajlUVTl5MFkwQUxLZHVXUWEiLCJtYWMiOiJiZGNjZWY4MjlmOTAzZGU0OTliMTVlNTY2NTk5MjI4ODljNDdmYWIwNTg3NTM3NWU2ODMzNDM4NTU3MjVkMzVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483672856\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1465766009 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60511</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"47 characters\">/index.php/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1099</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1099</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkdwaUptYldUSmF6WnMxUDM4em9YQ0E9PSIsInZhbHVlIjoieVJBS3djaVhhVFZmdFhvbkQ3ZXVyZEJWQm5DeFZRNjk4TzJicE1yak5MVUc3bEF5WmtjM2RaZmI0ci8zbFpVa0tBV2hDdDlpcC9rL1dyaUtKc0VsaW5jRStRWmxBMmcyK2ZvODR3STI5aCsvcWxMdkIzb2lFODNWSTEwM2FSWTMiLCJtYWMiOiIzZTdiY2QwYjZlODYyMTJjMThhNThkNDRmNzE1MDUyMjA1NDU2M2Y1N2JiMTcyN2FmNmRjZGFhZjQ0MTZhOWZjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkxWd2ZTWHdQRzF3UkVSMUVyS0REZmc9PSIsInZhbHVlIjoiUFdEMGJQM1NySCt1R3NJK1ArdFgrTnFOY3FCdlh3QmJNYVBrMGE0QWFuL3EvbnBFRFpab1lnWEQrdCtzWGdKc3RYUVlvd2tLUVNZWGVCS2tOc2NNM1B5blFkSUtBb3JEZmZ3dkh2VlhUZzZsdlBXajlUVTl5MFkwQUxLZHVXUWEiLCJtYWMiOiJiZGNjZWY4MjlmOTAzZGU0OTliMTVlNTY2NTk5MjI4ODljNDdmYWIwNTg3NTM3NWU2ODMzNDM4NTU3MjVkMzVkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751383335.0433</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751383335</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465766009\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-433366831 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3MQqMzZAJmil6GWNRTwZQGoMQJ2F3HRZ41OmdrAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433366831\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2133453857 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:22:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlpVbUFJVVFlc1J3NkJnUTcrMmtiT2c9PSIsInZhbHVlIjoiUy84YmFYU1JObHlRSDVVcEdoNlZwRTNMOEJtWUNHbzNVRlVtTGNVNWdIejVsdnd4aTVOZXpTU3U3Q3dyRXE5L2pjenBvNWxnN1lqazJmaU1rbDEwb0tZOWlPTSt6QkxqdmVFWWlnOHBka2J2LzZudmVHa3ZkWWsxbkVBMWZXQzIiLCJtYWMiOiJlNTFmNGZkNjIyZWYzMzg5YTYyZGY0YjI3YTcyY2ViY2JkYzYxMGE1MjhhOTNlMWYzMWI2MGMzYjQ1YTg5Y2VmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:22:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6Ik0wTmU1VlBxT1FiVkNNWXZrNmFVTXc9PSIsInZhbHVlIjoiNVdzUGpURUdmaFBGaVZnelBzNDZFZkFCeHA1ZGRVL2Q0MUZPWjlXejQ5Mnhrb1g2SDQ2YmtCQmVNUmtvVlIwajRQR2kvOHRPTUU1WmZKVHp2dGlBWExLbTk0bjRnQlNYK1VxU3JCMGRYNmNWLzdWVmJZLyttSzAzWWEyRGgrZW4iLCJtYWMiOiIyYjcwMWE5ZTFjMzdmY2Q1YjE1ZWVkY2Q4ZWVmM2FjMTMxN2MyMjIxODBkNDlhYWFmMWM5ZTk4ZWQxMjI2NWY1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:22:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlpVbUFJVVFlc1J3NkJnUTcrMmtiT2c9PSIsInZhbHVlIjoiUy84YmFYU1JObHlRSDVVcEdoNlZwRTNMOEJtWUNHbzNVRlVtTGNVNWdIejVsdnd4aTVOZXpTU3U3Q3dyRXE5L2pjenBvNWxnN1lqazJmaU1rbDEwb0tZOWlPTSt6QkxqdmVFWWlnOHBka2J2LzZudmVHa3ZkWWsxbkVBMWZXQzIiLCJtYWMiOiJlNTFmNGZkNjIyZWYzMzg5YTYyZGY0YjI3YTcyY2ViY2JkYzYxMGE1MjhhOTNlMWYzMWI2MGMzYjQ1YTg5Y2VmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:22:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6Ik0wTmU1VlBxT1FiVkNNWXZrNmFVTXc9PSIsInZhbHVlIjoiNVdzUGpURUdmaFBGaVZnelBzNDZFZkFCeHA1ZGRVL2Q0MUZPWjlXejQ5Mnhrb1g2SDQ2YmtCQmVNUmtvVlIwajRQR2kvOHRPTUU1WmZKVHp2dGlBWExLbTk0bjRnQlNYK1VxU3JCMGRYNmNWLzdWVmJZLyttSzAzWWEyRGgrZW4iLCJtYWMiOiIyYjcwMWE5ZTFjMzdmY2Q1YjE1ZWVkY2Q4ZWVmM2FjMTMxN2MyMjIxODBkNDlhYWFmMWM5ZTk4ZWQxMjI2NWY1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:22:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133453857\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1173367202 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"186 characters\">http://localhost:8000/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png?expires=1751385088&amp;signature=61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173367202\", {\"maxDepth\":0})</script>\n"}}