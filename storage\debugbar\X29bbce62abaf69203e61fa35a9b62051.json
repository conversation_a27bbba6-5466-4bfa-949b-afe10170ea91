{"__meta": {"id": "X29bbce62abaf69203e61fa35a9b62051", "datetime": "2025-07-01 18:07:12", "utime": 1751382432.887216, "method": "POST", "uri": "/livewire/message/multi-step-register", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751382432.389079, "end": 1751382432.887251, "duration": 0.4981718063354492, "duration_str": "498ms", "measures": [{"label": "Booting", "start": 1751382432.389079, "relative_start": 0, "end": 1751382432.743897, "relative_end": 1751382432.743897, "duration": 0.35481786727905273, "duration_str": "355ms", "params": [], "collector": null}, {"label": "Application", "start": 1751382432.744477, "relative_start": 0.35539793968200684, "end": 1751382432.887253, "relative_end": 2.1457672119140625e-06, "duration": 0.1427760124206543, "duration_str": "143ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26331696, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.multi-step-register (\\resources\\views\\livewire\\multi-step-register.blade.php)", "param_count": 17, "params": ["errors", "_instance", "newUser", "niveau", "parcour", "parcours", "cv", "diplome", "releveb<PERSON><PERSON>", "releve1", "releve2", "releve3", "releve4", "terms", "iteration", "currentStep", "previewDocument"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/multi-step-register.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00633, "accumulated_duration_str": "6.33ms", "statements": [{"sql": "select * from `mentions` where `mentions`.`id` in (1, 2, 3, 4, 5, 6, 7, 11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00633, "duration_str": "6.33ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Mention": 9}, "count": 9}, "livewire": {"data": {"multi-step-register #VvaUPhLC1c7TTuD1bEMm": "array:5 [\n  \"data\" => array:15 [\n    \"newUser\" => array:13 [\n      \"sexe\" => \"F\"\n      \"date_naissance\" => \"1995-08-07\"\n      \"lieu_naissance\" => \"In rerum recusandae\"\n      \"adresse\" => \"Voluptates ut tempor\"\n      \"nationalite\" => \"Sed aut odio volupta\"\n      \"telephone2\" => \"+18795584611\"\n      \"cin\" => \"123412341234\"\n      \"date_delivrance\" => \"2020-01-24\"\n      \"lieu_delivrance\" => \"+****************\"\n      \"nom\" => \"Aphrodite\"\n      \"prenom\" => \"Macdonald\"\n      \"email\" => \"<EMAIL>\"\n      \"telephone1\" => \"+261 32 55 516 50\"\n    ]\n    \"niveau\" => \"1\"\n    \"parcour\" => 1\n    \"parcours\" => Illuminate\\Database\\Eloquent\\Collection {#1714\n      #items: array:9 [\n        0 => App\\Models\\Mention {#1712\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 1\n            \"nom\" => \"Banques Et Assurances\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 1\n            \"nom\" => \"Banques Et Assurances\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Mention {#1710\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Mention {#1709\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 3\n            \"nom\" => \"Finance, Comptabilité et Audit\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 3\n            \"nom\" => \"Finance, Comptabilité et Audit\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Mention {#1708\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 4\n            \"nom\" => \"Tourisme hôtellerie\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 4\n            \"nom\" => \"Tourisme hôtellerie\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Mention {#1707\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Communication et Marketing\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Communication et Marketing\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Mention {#1706\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 6\n            \"nom\" => \"Génie Electrique et Informatique Industrielle\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 6\n            \"nom\" => \"Génie Electrique et Informatique Industrielle\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Mention {#1705\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 7\n            \"nom\" => \"Génie Civil\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 7\n            \"nom\" => \"Génie Civil\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Mention {#1703\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 11\n            \"nom\" => \"Génie Logiciel et Systèmes\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 11\n            \"nom\" => \"Génie Logiciel et Systèmes\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Mention {#1704\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 8\n            \"nom\" => \"Génie Mécanique\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 8\n            \"nom\" => \"Génie Mécanique\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"cv\" => null\n    \"diplome\" => null\n    \"relevebacc\" => Livewire\\TemporaryUploadedFile {#1696\n      +\"disk\": \"local\"\n      -test: false\n      -originalName: \"ELVNyRBdEfS9EpQ0t3cH2z9njGRggV-metaQ291cnMgQ29tcHRhIEZpLnBkZg==-.pdf\"\n      -mimeType: \"application/octet-stream\"\n      -error: 0\n      #hashName: null\n      #storage: Illuminate\\Filesystem\\FilesystemAdapter {#1672\n        #driver: League\\Flysystem\\Filesystem {#1674\n          -config: League\\Flysystem\\Config {#736\n            -options: []\n          }\n          -pathNormalizer: League\\Flysystem\\WhitespacePathNormalizer {#1675}\n          -adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#1458\n            -prefixer: League\\Flysystem\\PathPrefixer {#1418\n              -prefix: \"C:\\xampp\\htdocs\\ImsaaFoad\\storage\\app\\\"\n              -separator: \"\\\"\n            }\n            -visibility: League\\Flysystem\\UnixVisibility\\PortableVisibilityConverter {#1525\n              -filePublic: 420\n              -filePrivate: 384\n              -directoryPublic: 493\n              -directoryPrivate: 448\n              -defaultForDirectories: \"private\"\n            }\n            -mimeTypeDetector: League\\Flysystem\\Local\\FallbackMimeTypeDetector {#1459\n              -detector: League\\MimeTypeDetection\\FinfoMimeTypeDetector {#1056\n                -finfo: finfo {#1673}\n                -extensionMap: League\\MimeTypeDetection\\GeneratedExtensionToMimeTypeMap {#1671}\n                -bufferSampleSize: null\n                -inconclusiveMimetypes: array:5 [\n                  0 => \"application/x-empty\"\n                  1 => \"text/plain\"\n                  2 => \"text/x-asm\"\n                  3 => \"application/octet-stream\"\n                  4 => \"inode/x-empty\"\n                ]\n              }\n              -inconclusiveMimetypes: array:5 [\n                0 => \"application/x-empty\"\n                1 => \"text/plain\"\n                2 => \"text/x-asm\"\n                3 => \"application/octet-stream\"\n                4 => \"inode/x-empty\"\n              ]\n            }\n            -rootLocation: \"C:\\xampp\\htdocs\\ImsaaFoad\\storage\\app\"\n            -rootLocationIsSetup: false\n            -writeFlags: 2\n            -linkHandling: 2\n          }\n          -publicUrlGenerator: null\n          -temporaryUrlGenerator: null\n        }\n        #adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#1458}\n        #config: array:3 [\n          \"driver\" => \"local\"\n          \"root\" => \"C:\\xampp\\htdocs\\ImsaaFoad\\storage\\app\"\n          \"throw\" => false\n        ]\n        #prefixer: League\\Flysystem\\PathPrefixer {#734\n          -prefix: \"C:\\xampp\\htdocs\\ImsaaFoad\\storage\\app\\\"\n          -separator: \"\\\"\n        }\n        #temporaryUrlCallback: null\n      }\n      #path: \"livewire-tmp/ELVNyRBdEfS9EpQ0t3cH2z9njGRggV-metaQ291cnMgQ29tcHRhIEZpLnBkZg==-.pdf\"\n      path: \"C:\\xampp\\htdocs\\ImsaaFoad\\storage\\app\\livewire-tmp\"\n      filename: \"ELVNyRBdEfS9EpQ0t3cH2z9njGRggV-metaQ291cnMgQ29tcHRhIEZpLnBkZg==-.pdf\"\n      basename: \"php2E74.tmp\"\n      pathname: \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\php2E74.tmp\"\n      extension: \"tmp\"\n      realPath: \"C:\\xampp\\htdocs\\ImsaaFoad\\storage\\app\\livewire-tmp/ELVNyRBdEfS9EpQ0t3cH2z9njGRggV-metaQ291cnMgQ29tcHRhIEZpLnBkZg==-.pdf\"\n      size: 527848\n      writable: false\n      readable: false\n      executable: false\n      file: false\n      dir: false\n      link: false\n    }\n    \"releve1\" => null\n    \"releve2\" => null\n    \"releve3\" => null\n    \"releve4\" => null\n    \"terms\" => null\n    \"iteration\" => 3\n    \"currentStep\" => \"documents\"\n    \"previewDocument\" => Livewire\\TemporaryUploadedFile {#1696}\n  ]\n  \"name\" => \"multi-step-register\"\n  \"view\" => \"livewire.multi-step-register\"\n  \"component\" => \"App\\Http\\Livewire\\MultiStepRegister\"\n  \"id\" => \"VvaUPhLC1c7TTuD1bEMm\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:13 [\n    \"sexe\" => \"F\"\n    \"date_naissance\" => \"1995-08-07\"\n    \"lieu_naissance\" => \"In rerum recusandae\"\n    \"adresse\" => \"Voluptates ut tempor\"\n    \"nationalite\" => \"Sed aut odio volupta\"\n    \"telephone2\" => \"+18795584611\"\n    \"cin\" => \"123412341234\"\n    \"date_delivrance\" => \"2020-01-24\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"nom\" => \"Aphrodite\"\n    \"prenom\" => \"Macdonald\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261 32 55 516 50\"\n  ]\n  \"niveau\" => \"1\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n]"}, "request": {"path_info": "/livewire/message/multi-step-register", "status_code": "<pre class=sf-dump id=sf-dump-1408614616 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1408614616\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1621709970 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1621709970\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1239885537 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">VvaUPhLC1c7TTuD1bEMm</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">multi-step-register</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newUser.telephone2</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">Format de t&#233;l&#233;phone invalide (ex: +261 XX XX XXX XX)</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">7f1c0d68</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n        \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-08-07</span>\"\n        \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">In rerum recusandae</span>\"\n        \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Voluptates ut tempor</span>\"\n        \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed aut odio volupta</span>\"\n        \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+18795584611</span>\"\n        \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n        \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2020-01-24</span>\"\n        \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Aphrodite</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Macdonald</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"19 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>parcours</span>\" => []\n      \"<span class=sf-dump-key>cv</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>diplome</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>relevebacc</span>\" => \"<span class=sf-dump-str title=\"82 characters\">livewire-file:ELVNyRBdEfS9EpQ0t3cH2z9njGRggV-metaQ291cnMgQ29tcHRhIEZpLnBkZg==-.pdf</span>\"\n      \"<span class=sf-dump-key>releve1</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve2</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve3</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve4</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>terms</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>iteration</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n      \"<span class=sf-dump-key>previewDocument</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcours</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Mention</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>4</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>5</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>6</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>7</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>11</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>8</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">2fa9906c27fdda941e29b9b6283a6f1861dc05044befa1654be739cc6285feec</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">wx6a</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">previewDocument</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">relevebacc</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239885537\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-895882018 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1305</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ii8yQ3BnYlFNTkRiZ3NmNWFoM2NSamc9PSIsInZhbHVlIjoieHdYajRmUTk4ZkdrM3FENVpQZ094c2lKQmNMUDJNV1pjOWFyYmtYOXFaOURUTFVOV2NoK3puUXpIZzRTNzNyQXh3aFgwU1dSQ2sxeHZIRHpIOEdpSmVaT0M3RXBJeG5JeUJ6bGR1MjFCbnBsZ3BjZEdheFFaUGprTVBLSWQ2cXkiLCJtYWMiOiIzMjBmOWNmZDM2MmJkNzBmMmU4NTgxZTg5NTIxNTNiNmI1MTM4MmUxZDMyZjFmMTgzNDcxZjE0MTE2N2U4OTk4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IlpibGVuUmZYQlRUZEhFc2tiZnBnS2c9PSIsInZhbHVlIjoialhPN0VnWE8rcVpMQitKVHNjZk5qdTVDTk5uWk84bUJvOXlVcmo3c2RwVzVnWDRKZEF3Q1hkeUJsWkd5K2FBV1FqUXlmU2ZOUFRaRlNtZHdvQ0xtSlZSaTJTSkcyT2JwMHJrdmc1VmM2RFZpU2xtS0pIczM3b2FtbDRCbk5KNy8iLCJtYWMiOiIyZGFiMTExZmMzZTExZTYxY2QwOTQ1MzBkYTIwODU4YTg0NDAwN2Q1ZjI2ZTdiMTA2NWE5MGY4MWIxYzBjY2QzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895882018\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-139015871 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59370</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"47 characters\">/index.php/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1305</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1305</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ii8yQ3BnYlFNTkRiZ3NmNWFoM2NSamc9PSIsInZhbHVlIjoieHdYajRmUTk4ZkdrM3FENVpQZ094c2lKQmNMUDJNV1pjOWFyYmtYOXFaOURUTFVOV2NoK3puUXpIZzRTNzNyQXh3aFgwU1dSQ2sxeHZIRHpIOEdpSmVaT0M3RXBJeG5JeUJ6bGR1MjFCbnBsZ3BjZEdheFFaUGprTVBLSWQ2cXkiLCJtYWMiOiIzMjBmOWNmZDM2MmJkNzBmMmU4NTgxZTg5NTIxNTNiNmI1MTM4MmUxZDMyZjFmMTgzNDcxZjE0MTE2N2U4OTk4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IlpibGVuUmZYQlRUZEhFc2tiZnBnS2c9PSIsInZhbHVlIjoialhPN0VnWE8rcVpMQitKVHNjZk5qdTVDTk5uWk84bUJvOXlVcmo3c2RwVzVnWDRKZEF3Q1hkeUJsWkd5K2FBV1FqUXlmU2ZOUFRaRlNtZHdvQ0xtSlZSaTJTSkcyT2JwMHJrdmc1VmM2RFZpU2xtS0pIczM3b2FtbDRCbk5KNy8iLCJtYWMiOiIyZGFiMTExZmMzZTExZTYxY2QwOTQ1MzBkYTIwODU4YTg0NDAwN2Q1ZjI2ZTdiMTA2NWE5MGY4MWIxYzBjY2QzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751382432.3891</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751382432</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139015871\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1584528623 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hRF2mVuiU5BibRd5o4CekB6Fo0oqlULGaPW0Otn7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584528623\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-28243515 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:07:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikdqam9sU0N5aitSNkxPNkNHVlc1ZlE9PSIsInZhbHVlIjoiZlZTYjgxdjBydVI4VHpmQ1NrOVBsQ2YyYlEyM0RjZWFvSjdCQmx2SHFPc3dkajdQbUJ5V1pDUVJiaS84UDgxVlA2amN4dllQUW9nY3NJOWlkcTYzamdDNXNRY294VVhrN2J6b3RybWhBRmZWRnA2SEJNU2wraTBYNmJrNTVINnoiLCJtYWMiOiJlNzAzYzE3MjU5MmZiZjUyYWEyZDk2NTE4OGFiMDFiOTM0YmVkMWE1ZmJlNGRlMGM4NTQxOGNjMDg2NmQ5NzkwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:07:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6InQ5S0xMNUVyejRGN1JXSmxFTFZYZXc9PSIsInZhbHVlIjoiTEw1QjNZeVVoTzBBM2Z0cWlsNlBxa0xEZlRUT1F3bmpHTW15aEFNeHFEc0hIVlQ3TExYVXpPeWxjbEJaQjJTc3ZrR1JiRDcwY1VGeXZVVTAwWGNjeVNvYUsxUXB0U2ZNWUgwMU05cWJFZFB4TnRrNVNicGtGQmF2ZUlqU0tlZ3QiLCJtYWMiOiJjMjc0OWM4MjQ5MzhhNGU2NzhiMzE4ZDUxYTc4MjZiNzkxODViMzQwYTViNzQxODI2ZmU3YjllMDBjM2VhZDQyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:07:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikdqam9sU0N5aitSNkxPNkNHVlc1ZlE9PSIsInZhbHVlIjoiZlZTYjgxdjBydVI4VHpmQ1NrOVBsQ2YyYlEyM0RjZWFvSjdCQmx2SHFPc3dkajdQbUJ5V1pDUVJiaS84UDgxVlA2amN4dllQUW9nY3NJOWlkcTYzamdDNXNRY294VVhrN2J6b3RybWhBRmZWRnA2SEJNU2wraTBYNmJrNTVINnoiLCJtYWMiOiJlNzAzYzE3MjU5MmZiZjUyYWEyZDk2NTE4OGFiMDFiOTM0YmVkMWE1ZmJlNGRlMGM4NTQxOGNjMDg2NmQ5NzkwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:07:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6InQ5S0xMNUVyejRGN1JXSmxFTFZYZXc9PSIsInZhbHVlIjoiTEw1QjNZeVVoTzBBM2Z0cWlsNlBxa0xEZlRUT1F3bmpHTW15aEFNeHFEc0hIVlQ3TExYVXpPeWxjbEJaQjJTc3ZrR1JiRDcwY1VGeXZVVTAwWGNjeVNvYUsxUXB0U2ZNWUgwMU05cWJFZFB4TnRrNVNicGtGQmF2ZUlqU0tlZ3QiLCJtYWMiOiJjMjc0OWM4MjQ5MzhhNGU2NzhiMzE4ZDUxYTc4MjZiNzkxODViMzQwYTViNzQxODI2ZmU3YjllMDBjM2VhZDQyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:07:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28243515\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1614787339 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-08-07</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">In rerum recusandae</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Voluptates ut tempor</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed aut odio volupta</span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+18795584611</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2020-01-24</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Aphrodite</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Macdonald</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"19 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1614787339\", {\"maxDepth\":0})</script>\n"}}