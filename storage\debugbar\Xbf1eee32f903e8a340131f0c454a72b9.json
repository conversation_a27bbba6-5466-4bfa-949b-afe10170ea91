{"__meta": {"id": "Xbf1eee32f903e8a340131f0c454a72b9", "datetime": "2025-07-01 18:22:28", "utime": 1751383348.284974, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751383347.518864, "end": 1751383348.285004, "duration": 0.7661399841308594, "duration_str": "766ms", "measures": [{"label": "Booting", "start": 1751383347.518864, "relative_start": 0, "end": 1751383348.069819, "relative_end": 1751383348.069819, "duration": 0.550955057144165, "duration_str": "551ms", "params": [], "collector": null}, {"label": "Application", "start": 1751383348.070637, "relative_start": 0.5517730712890625, "end": 1751383348.285007, "relative_end": 3.0994415283203125e-06, "duration": 0.2143700122833252, "duration_str": "214ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 24188816, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "foadregister (\\resources\\views\\foadregister.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/foadregister.blade.php&line=0"}, {"name": "livewire.multi-step-register (\\resources\\views\\livewire\\multi-step-register.blade.php)", "param_count": 17, "params": ["errors", "_instance", "newUser", "niveau", "parcour", "parcours", "cv", "diplome", "releveb<PERSON><PERSON>", "releve1", "releve2", "releve3", "releve4", "terms", "iteration", "currentStep", "previewDocument"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/multi-step-register.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 4, "params": ["__env", "app", "errors", "html"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "\\Illuminate\\Routing\\ViewController@__invoke", "controller": "\\Illuminate\\Routing\\ViewController", "namespace": null, "prefix": "", "where": [], "as": "register"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": {"multi-step-register #AXHzs4fjUJx369JcMkxV": "array:5 [\n  \"data\" => array:15 [\n    \"newUser\" => []\n    \"niveau\" => null\n    \"parcour\" => null\n    \"parcours\" => Illuminate\\Support\\Collection {#758\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"cv\" => null\n    \"diplome\" => null\n    \"relevebacc\" => null\n    \"releve1\" => null\n    \"releve2\" => null\n    \"releve3\" => null\n    \"releve4\" => null\n    \"terms\" => null\n    \"iteration\" => null\n    \"currentStep\" => \"parcours\"\n    \"previewDocument\" => null\n  ]\n  \"name\" => \"multi-step-register\"\n  \"view\" => \"livewire.multi-step-register\"\n  \"component\" => \"App\\Http\\Livewire\\MultiStepRegister\"\n  \"id\" => \"AXHzs4fjUJx369JcMkxV\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-981635764 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-981635764\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1840176840 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1840176840\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-96003582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-96003582\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-653693538 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IjRDOVNoUytnUGVseDRPQXgxOVN1M0E9PSIsInZhbHVlIjoid09jT0ZtQUtYVTVtWDk4bUw2RHA4NjVOZEw2N2xnN0QvTHR2MXh1V3gvcWVFT1R5cElwMVhOR3JXL21XQlh6ZUZRLytuQ09QdTUrZmtldHdIYUl0aEtWSmZtWE0rTjJXUWRrWm81cEZUVXBZYXlHU1BINnMwOGRCWDNPeUoyQXgiLCJtYWMiOiJiOTNjYjhlYjY3NmMxMjhhY2Q2NzlmZTgyY2RmZDQwZTEyYjU2YWVmNGQyOTgwOGNhMjUyYTU3Nzc4NTRlYmVhIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImEvZ3ordVRXSXFWZEZpSEpEaUYvckE9PSIsInZhbHVlIjoiblZjM2RlWDBmVFptOFQ3VGRFUWtyekFUYmR5QmJLa3lONGdHN2p1LzM5SmRIS3N0UDdoT29KdUZTNTJ1M3A5U3FVUmY1VGVtZEppSU1ITk9kYngrdUI5aUVBYVdWZEtrcEtiTlptUVdrMzQ1WTZwVkVDM01vWDF4RXpIZWlyVFEiLCJtYWMiOiI0M2NjODQ5OTc4M2EyMzk2YTFhNjczNmEwMDE4ZmJjZjhhM2ZiZTBkN2Y2MjJkOTgxMWQyMzI5MTQ4MmVlMTEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653693538\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-262458990 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60540</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IjRDOVNoUytnUGVseDRPQXgxOVN1M0E9PSIsInZhbHVlIjoid09jT0ZtQUtYVTVtWDk4bUw2RHA4NjVOZEw2N2xnN0QvTHR2MXh1V3gvcWVFT1R5cElwMVhOR3JXL21XQlh6ZUZRLytuQ09QdTUrZmtldHdIYUl0aEtWSmZtWE0rTjJXUWRrWm81cEZUVXBZYXlHU1BINnMwOGRCWDNPeUoyQXgiLCJtYWMiOiJiOTNjYjhlYjY3NmMxMjhhY2Q2NzlmZTgyY2RmZDQwZTEyYjU2YWVmNGQyOTgwOGNhMjUyYTU3Nzc4NTRlYmVhIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImEvZ3ordVRXSXFWZEZpSEpEaUYvckE9PSIsInZhbHVlIjoiblZjM2RlWDBmVFptOFQ3VGRFUWtyekFUYmR5QmJLa3lONGdHN2p1LzM5SmRIS3N0UDdoT29KdUZTNTJ1M3A5U3FVUmY1VGVtZEppSU1ITk9kYngrdUI5aUVBYVdWZEtrcEtiTlptUVdrMzQ1WTZwVkVDM01vWDF4RXpIZWlyVFEiLCJtYWMiOiI0M2NjODQ5OTc4M2EyMzk2YTFhNjczNmEwMDE4ZmJjZjhhM2ZiZTBkN2Y2MjJkOTgxMWQyMzI5MTQ4MmVlMTEyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751383347.5189</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751383347</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262458990\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1992116297 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3MQqMzZAJmil6GWNRTwZQGoMQJ2F3HRZ41OmdrAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992116297\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1893375909 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:22:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9ZOG9wVXR6UktvRFlBYkVCQnBVdlE9PSIsInZhbHVlIjoiYi9WL2t6OHFjbGFyVUsremxCNEdoc3BXeEVBTFhYSHlGZ3k5My9CTHVhTXd3K2ZlR2I5Q1BmRmhDNERGOCtickdOVFNsa1NpZnBwczUxM29yaTZ1NldpZFJwWlZGdExmQXVyVE9wMkZNOGhuV2h2TUFhRWJEMVM1L1gwSEZjZ04iLCJtYWMiOiIyZTk4OWJlMjE5NmFlMzU2ODM3YmExNTBkYmJiN2JkNDExMzc5ZDU2MTI5MzVmMzZiNzlkZTVhYmQ0YzUzMzBmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:22:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6InNYSG96L2ROaTVQcHA0U2piYTM1Snc9PSIsInZhbHVlIjoiTkdQK1lEbURpRlNpUDZCL1FmK3YzdkNtL2ZvRTFSemtEaitnOHNDb2JRMEx1ZTRpb1UxYjIwTGtxaEZPWFpEcThETnVFT2t0L3E0K3duWHB5eThic1dJZExySjRpMEdCeFhmY2hzb1RNdGgxQTVhdzJtZmM5VEZYSkFTMHl5K2oiLCJtYWMiOiI5YmQzMzcwMDU0ZjhlNDJjM2YyMmMyZDhmNWEwYzAzMTUyN2M5ZDMxMGU1ZDgwMTdkNGNlNTQ0MTRkZGVlMTBiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:22:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9ZOG9wVXR6UktvRFlBYkVCQnBVdlE9PSIsInZhbHVlIjoiYi9WL2t6OHFjbGFyVUsremxCNEdoc3BXeEVBTFhYSHlGZ3k5My9CTHVhTXd3K2ZlR2I5Q1BmRmhDNERGOCtickdOVFNsa1NpZnBwczUxM29yaTZ1NldpZFJwWlZGdExmQXVyVE9wMkZNOGhuV2h2TUFhRWJEMVM1L1gwSEZjZ04iLCJtYWMiOiIyZTk4OWJlMjE5NmFlMzU2ODM3YmExNTBkYmJiN2JkNDExMzc5ZDU2MTI5MzVmMzZiNzlkZTVhYmQ0YzUzMzBmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:22:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6InNYSG96L2ROaTVQcHA0U2piYTM1Snc9PSIsInZhbHVlIjoiTkdQK1lEbURpRlNpUDZCL1FmK3YzdkNtL2ZvRTFSemtEaitnOHNDb2JRMEx1ZTRpb1UxYjIwTGtxaEZPWFpEcThETnVFT2t0L3E0K3duWHB5eThic1dJZExySjRpMEdCeFhmY2hzb1RNdGgxQTVhdzJtZmM5VEZYSkFTMHl5K2oiLCJtYWMiOiI5YmQzMzcwMDU0ZjhlNDJjM2YyMmMyZDhmNWEwYzAzMTUyN2M5ZDMxMGU1ZDgwMTdkNGNlNTQ0MTRkZGVlMTBiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:22:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893375909\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-123435909 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123435909\", {\"maxDepth\":0})</script>\n"}}