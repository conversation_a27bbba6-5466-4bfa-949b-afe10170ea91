{"__meta": {"id": "Xa79e6c076b247cf30f47a40bbae5fec5", "datetime": "2025-07-01 18:22:24", "utime": 1751383344.070191, "method": "GET", "uri": "/registration-success?name=qdfqs%20qsdfqsdf&email=edwino%40qsdfqm.com", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751383343.193062, "end": 1751383344.070222, "duration": 0.877159833908081, "duration_str": "877ms", "measures": [{"label": "Booting", "start": 1751383343.193062, "relative_start": 0, "end": 1751383343.868177, "relative_end": 1751383343.868177, "duration": 0.6751148700714111, "duration_str": "675ms", "params": [], "collector": null}, {"label": "Application", "start": 1751383343.868901, "relative_start": 0.6758389472961426, "end": 1751383344.070224, "relative_end": 2.1457672119140625e-06, "duration": 0.2013230323791504, "duration_str": "201ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23058456, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "success (\\resources\\views\\success.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/success.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET registration-success", "middleware": "web", "uses": "\\Illuminate\\Routing\\ViewController@__invoke", "controller": "\\Illuminate\\Routing\\ViewController", "namespace": null, "prefix": "", "where": [], "as": "registration.success"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/registration-success?email=edwino%40qsdfqm.com&name=qdfqs%20qsdfqsdf\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/registration-success", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-237539783 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">qdfqs qsdfqsdf</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237539783\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-561086870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-561086870\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-923162259 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IlpVbUFJVVFlc1J3NkJnUTcrMmtiT2c9PSIsInZhbHVlIjoiUy84YmFYU1JObHlRSDVVcEdoNlZwRTNMOEJtWUNHbzNVRlVtTGNVNWdIejVsdnd4aTVOZXpTU3U3Q3dyRXE5L2pjenBvNWxnN1lqazJmaU1rbDEwb0tZOWlPTSt6QkxqdmVFWWlnOHBka2J2LzZudmVHa3ZkWWsxbkVBMWZXQzIiLCJtYWMiOiJlNTFmNGZkNjIyZWYzMzg5YTYyZGY0YjI3YTcyY2ViY2JkYzYxMGE1MjhhOTNlMWYzMWI2MGMzYjQ1YTg5Y2VmIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6Ik0wTmU1VlBxT1FiVkNNWXZrNmFVTXc9PSIsInZhbHVlIjoiNVdzUGpURUdmaFBGaVZnelBzNDZFZkFCeHA1ZGRVL2Q0MUZPWjlXejQ5Mnhrb1g2SDQ2YmtCQmVNUmtvVlIwajRQR2kvOHRPTUU1WmZKVHp2dGlBWExLbTk0bjRnQlNYK1VxU3JCMGRYNmNWLzdWVmJZLyttSzAzWWEyRGgrZW4iLCJtYWMiOiIyYjcwMWE5ZTFjMzdmY2Q1YjE1ZWVkY2Q4ZWVmM2FjMTMxN2MyMjIxODBkNDlhYWFmMWM5ZTk4ZWQxMjI2NWY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923162259\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-274919952 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60521</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"69 characters\">/registration-success?name=qdfqs%20qsdfqsdf&amp;email=edwino%40qsdfqm.com</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/registration-success</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/registration-success</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"47 characters\">name=qdfqs%20qsdfqsdf&amp;email=edwino%40qsdfqm.com</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IlpVbUFJVVFlc1J3NkJnUTcrMmtiT2c9PSIsInZhbHVlIjoiUy84YmFYU1JObHlRSDVVcEdoNlZwRTNMOEJtWUNHbzNVRlVtTGNVNWdIejVsdnd4aTVOZXpTU3U3Q3dyRXE5L2pjenBvNWxnN1lqazJmaU1rbDEwb0tZOWlPTSt6QkxqdmVFWWlnOHBka2J2LzZudmVHa3ZkWWsxbkVBMWZXQzIiLCJtYWMiOiJlNTFmNGZkNjIyZWYzMzg5YTYyZGY0YjI3YTcyY2ViY2JkYzYxMGE1MjhhOTNlMWYzMWI2MGMzYjQ1YTg5Y2VmIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6Ik0wTmU1VlBxT1FiVkNNWXZrNmFVTXc9PSIsInZhbHVlIjoiNVdzUGpURUdmaFBGaVZnelBzNDZFZkFCeHA1ZGRVL2Q0MUZPWjlXejQ5Mnhrb1g2SDQ2YmtCQmVNUmtvVlIwajRQR2kvOHRPTUU1WmZKVHp2dGlBWExLbTk0bjRnQlNYK1VxU3JCMGRYNmNWLzdWVmJZLyttSzAzWWEyRGgrZW4iLCJtYWMiOiIyYjcwMWE5ZTFjMzdmY2Q1YjE1ZWVkY2Q4ZWVmM2FjMTMxN2MyMjIxODBkNDlhYWFmMWM5ZTk4ZWQxMjI2NWY1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751383343.1931</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751383343</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274919952\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-193192258 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3MQqMzZAJmil6GWNRTwZQGoMQJ2F3HRZ41OmdrAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193192258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-292375235 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:22:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjRDOVNoUytnUGVseDRPQXgxOVN1M0E9PSIsInZhbHVlIjoid09jT0ZtQUtYVTVtWDk4bUw2RHA4NjVOZEw2N2xnN0QvTHR2MXh1V3gvcWVFT1R5cElwMVhOR3JXL21XQlh6ZUZRLytuQ09QdTUrZmtldHdIYUl0aEtWSmZtWE0rTjJXUWRrWm81cEZUVXBZYXlHU1BINnMwOGRCWDNPeUoyQXgiLCJtYWMiOiJiOTNjYjhlYjY3NmMxMjhhY2Q2NzlmZTgyY2RmZDQwZTEyYjU2YWVmNGQyOTgwOGNhMjUyYTU3Nzc4NTRlYmVhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:22:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6ImEvZ3ordVRXSXFWZEZpSEpEaUYvckE9PSIsInZhbHVlIjoiblZjM2RlWDBmVFptOFQ3VGRFUWtyekFUYmR5QmJLa3lONGdHN2p1LzM5SmRIS3N0UDdoT29KdUZTNTJ1M3A5U3FVUmY1VGVtZEppSU1ITk9kYngrdUI5aUVBYVdWZEtrcEtiTlptUVdrMzQ1WTZwVkVDM01vWDF4RXpIZWlyVFEiLCJtYWMiOiI0M2NjODQ5OTc4M2EyMzk2YTFhNjczNmEwMDE4ZmJjZjhhM2ZiZTBkN2Y2MjJkOTgxMWQyMzI5MTQ4MmVlMTEyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:22:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjRDOVNoUytnUGVseDRPQXgxOVN1M0E9PSIsInZhbHVlIjoid09jT0ZtQUtYVTVtWDk4bUw2RHA4NjVOZEw2N2xnN0QvTHR2MXh1V3gvcWVFT1R5cElwMVhOR3JXL21XQlh6ZUZRLytuQ09QdTUrZmtldHdIYUl0aEtWSmZtWE0rTjJXUWRrWm81cEZUVXBZYXlHU1BINnMwOGRCWDNPeUoyQXgiLCJtYWMiOiJiOTNjYjhlYjY3NmMxMjhhY2Q2NzlmZTgyY2RmZDQwZTEyYjU2YWVmNGQyOTgwOGNhMjUyYTU3Nzc4NTRlYmVhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:22:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6ImEvZ3ordVRXSXFWZEZpSEpEaUYvckE9PSIsInZhbHVlIjoiblZjM2RlWDBmVFptOFQ3VGRFUWtyekFUYmR5QmJLa3lONGdHN2p1LzM5SmRIS3N0UDdoT29KdUZTNTJ1M3A5U3FVUmY1VGVtZEppSU1ITk9kYngrdUI5aUVBYVdWZEtrcEtiTlptUVdrMzQ1WTZwVkVDM01vWDF4RXpIZWlyVFEiLCJtYWMiOiI0M2NjODQ5OTc4M2EyMzk2YTFhNjczNmEwMDE4ZmJjZjhhM2ZiZTBkN2Y2MjJkOTgxMWQyMzI5MTQ4MmVlMTEyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:22:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292375235\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1727805134 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"90 characters\">http://localhost:8000/registration-success?email=edwino%40qsdfqm.com&amp;name=qdfqs%20qsdfqsdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727805134\", {\"maxDepth\":0})</script>\n"}}