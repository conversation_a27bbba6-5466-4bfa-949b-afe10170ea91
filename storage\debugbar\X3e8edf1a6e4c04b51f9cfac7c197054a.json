{"__meta": {"id": "X3e8edf1a6e4c04b51f9cfac7c197054a", "datetime": "2025-07-01 17:39:43", "utime": 1751380783.292114, "method": "POST", "uri": "/livewire/upload-file?expires=1751381081&signature=905672056b02073a30f055d13eb8540dc40ba5f82aa2f80a259b852bef263ee0", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380782.499588, "end": 1751380783.292141, "duration": 0.7925529479980469, "duration_str": "793ms", "measures": [{"label": "Booting", "start": 1751380782.499588, "relative_start": 0, "end": 1751380783.046616, "relative_end": 1751380783.046616, "duration": 0.5470280647277832, "duration_str": "547ms", "params": [], "collector": null}, {"label": "Application", "start": 1751380783.047591, "relative_start": 0.5480029582977295, "end": 1751380783.292144, "relative_end": 3.0994415283203125e-06, "duration": 0.2445530891418457, "duration_str": "245ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26436016, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/upload-file", "controller": "Livewire\\Controllers\\FileUploadHandler@handle", "as": "livewire.upload-file", "middleware": "web", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php&line=19\">\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php:19-28</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:13 [\n    \"sexe\" => \"H\"\n    \"nom\" => \"Rama\"\n    \"prenom\" => \"<PERSON>\"\n    \"date_naissance\" => \"1993-02-18\"\n    \"lieu_naissance\" => \"Ad et veniam exerci\"\n    \"adresse\" => \"Facere dicta ratione\"\n    \"nationalite\" => \"Sed odio quo sit ea \"\n    \"telephone2\" => \"1234123\"\n    \"cin\" => \"+****************\"\n    \"date_delivrance\" => \"1995-02-23\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261325551650\"\n  ]\n  \"niveau\" => \"1\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n]"}, "request": {"path_info": "/livewire/upload-file", "status_code": "<pre class=sf-dump id=sf-dump-1726917559 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1726917559\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2008380766 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1751381081</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">905672056b02073a30f055d13eb8540dc40ba5f82aa2f80a259b852bef263ee0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008380766\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1667386507 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1667386507\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-117316238 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">528049</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarynpPI4SRCBQsPdiRW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IlRxSDFmRTVnd3ozVWFpNCtwWmVKNHc9PSIsInZhbHVlIjoiemdIZVlQbU1tVjFGQTUvOVVGZWNvQ3doQUZIQnZwYllPYllSdlQvSkY3Z0o2QXRWTzZsb2h4dGxodlFxYlV3MW5BZVV0OTNPU2RwM2R3dSt3Rnh2ZzBDb2lTQXl4WVZ0Y1JXMWJYZmFMSFdyMVJOOVJHT0hEYTZxREJ2cElIci8iLCJtYWMiOiIzN2FiOWEwNDg2YjIwOWZjN2YxMWY0ZTA0NjJjMDlmNzVlZTE5ODQ3OWE0ZTk4YTlhZWNkOGQ3ZGY2YzU5ZjUyIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImVxNmVNS2lhOGltUHFYK1VNSFZyUHc9PSIsInZhbHVlIjoibGdONFNuVjBIT1BxNGN3TklFcThyUnp4Z2lKbzdncG1CWGlXc2VtUzgyNmIvNmtlNGZEV2hmbG5senBMRFJ6aGNVeFdobGoyZ0s1aEZmTm13K2hiaGNndWFjZmhUT3dRMHNCeHJqNEZ0T0M0RHNibk9wUFhkVXlCYTZMUitIMGQiLCJtYWMiOiJmOGQ3YmFhYTExNzNlNzE5MDU0Mzc4YWNiODkzYzJlOTdjNjYzZjY5YzcxNzJlNDYxZjU4YjQwNDJlMzkzY2ZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117316238\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-176321336 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57911</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"115 characters\">/livewire/upload-file?expires=1751381081&amp;signature=905672056b02073a30f055d13eb8540dc40ba5f82aa2f80a259b852bef263ee0</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"93 characters\">expires=1751381081&amp;signature=905672056b02073a30f055d13eb8540dc40ba5f82aa2f80a259b852bef263ee0</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"6 characters\">528049</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"6 characters\">528049</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarynpPI4SRCBQsPdiRW</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarynpPI4SRCBQsPdiRW</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IlRxSDFmRTVnd3ozVWFpNCtwWmVKNHc9PSIsInZhbHVlIjoiemdIZVlQbU1tVjFGQTUvOVVGZWNvQ3doQUZIQnZwYllPYllSdlQvSkY3Z0o2QXRWTzZsb2h4dGxodlFxYlV3MW5BZVV0OTNPU2RwM2R3dSt3Rnh2ZzBDb2lTQXl4WVZ0Y1JXMWJYZmFMSFdyMVJOOVJHT0hEYTZxREJ2cElIci8iLCJtYWMiOiIzN2FiOWEwNDg2YjIwOWZjN2YxMWY0ZTA0NjJjMDlmNzVlZTE5ODQ3OWE0ZTk4YTlhZWNkOGQ3ZGY2YzU5ZjUyIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImVxNmVNS2lhOGltUHFYK1VNSFZyUHc9PSIsInZhbHVlIjoibGdONFNuVjBIT1BxNGN3TklFcThyUnp4Z2lKbzdncG1CWGlXc2VtUzgyNmIvNmtlNGZEV2hmbG5senBMRFJ6aGNVeFdobGoyZ0s1aEZmTm13K2hiaGNndWFjZmhUT3dRMHNCeHJqNEZ0T0M0RHNibk9wUFhkVXlCYTZMUitIMGQiLCJtYWMiOiJmOGQ3YmFhYTExNzNlNzE5MDU0Mzc4YWNiODkzYzJlOTdjNjYzZjY5YzcxNzJlNDYxZjU4YjQwNDJlMzkzY2ZmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380782.4996</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380782</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176321336\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-234008809 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yVCdhQsriTuZTEQBdHuoxzYc5BgY1Mo8vp1abE4A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234008809\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-318318921 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:39:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ijk0ZlFqK3FlanpDSTNjYUxsYWdxbmc9PSIsInZhbHVlIjoiTFZzaStoRTY5aGVLbC9Lb1RDcXArcWhFQzRrUHQ1NWNGS3F5U1pEVVhvZkRMZVJ5Q2dJZGVTNUNmb2pJQnJnRldISHdQQU5EOEl0WVJraE1DWkk3dW9xYWRveXF2QkxISVcvb2xrYkhrWC8vNlp2SU44RnBiUWRSQm9PdFZlSmsiLCJtYWMiOiIzZTJhMmVmMDBlN2IzNDg5YTNkZTY5MmUyOGFkYzA0YzhiZjQ3NzM2ZWM0OGJmMTk1MTU3N2Q0M2Y1NDE0ODk1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:39:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6Ing0UklsdDUzNk9LUHRaTG5zamlWbHc9PSIsInZhbHVlIjoibFBVN0VLYThEdVB4Q3R6Wlh3VnRCZTVnRXZKVDRKSXBpaDJ3cnY5UUFvemFIYUVxdXJuSkpKemRRRDFRRjRYckZDdjhtYUdLNlBqaWU3VmpreGhyS3puM2F0Z2k3eUtZajArWEVvNzZ5Ny9PZmFWQWw0UEhmL0hMaHkvWGVMUGgiLCJtYWMiOiIwNmI4Yzg5NTE4YzJjYjJmMzEwOGM2OWEwNDkwMjU5ZmQ1MTQ0NjdhNGRkMGFkYzdiOWFhMjdlM2VhNzg2NWEyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:39:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ijk0ZlFqK3FlanpDSTNjYUxsYWdxbmc9PSIsInZhbHVlIjoiTFZzaStoRTY5aGVLbC9Lb1RDcXArcWhFQzRrUHQ1NWNGS3F5U1pEVVhvZkRMZVJ5Q2dJZGVTNUNmb2pJQnJnRldISHdQQU5EOEl0WVJraE1DWkk3dW9xYWRveXF2QkxISVcvb2xrYkhrWC8vNlp2SU44RnBiUWRSQm9PdFZlSmsiLCJtYWMiOiIzZTJhMmVmMDBlN2IzNDg5YTNkZTY5MmUyOGFkYzA0YzhiZjQ3NzM2ZWM0OGJmMTk1MTU3N2Q0M2Y1NDE0ODk1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:39:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6Ing0UklsdDUzNk9LUHRaTG5zamlWbHc9PSIsInZhbHVlIjoibFBVN0VLYThEdVB4Q3R6Wlh3VnRCZTVnRXZKVDRKSXBpaDJ3cnY5UUFvemFIYUVxdXJuSkpKemRRRDFRRjRYckZDdjhtYUdLNlBqaWU3VmpreGhyS3puM2F0Z2k3eUtZajArWEVvNzZ5Ny9PZmFWQWw0UEhmL0hMaHkvWGVMUGgiLCJtYWMiOiIwNmI4Yzg5NTE4YzJjYjJmMzEwOGM2OWEwNDkwMjU5ZmQ1MTQ0NjdhNGRkMGFkYzdiOWFhMjdlM2VhNzg2NWEyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:39:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318318921\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1276603780 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rama</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Dyer</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1993-02-18</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Ad et veniam exerci</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Facere dicta ratione</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed odio quo sit ea </span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-02-23</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+261325551650</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276603780\", {\"maxDepth\":0})</script>\n"}}