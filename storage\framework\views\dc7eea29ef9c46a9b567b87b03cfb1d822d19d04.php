 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Etudiants
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Etudiants
             </h3>
         </div>
         <div class="block-content block-content-full">
             <div class="row">
                 <div class="col-sm-12 col-md-6">
                     <div class="d-flex">
                         <div>
                             <label>
                                 <select wire:model="filtreParcours" class="form-select form-select-sm">
                                     <option selected value="">Filtre Parcours</option>
                                     <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                         <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?></option>
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                     <option selected value="">Filtre Niveau</option>
                                     <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                         <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                     <option selected value="">Filtre Année</option>
                                     <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                         <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                 </select>
                             </label>
                         </div>

                     </div>

                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter js-dataTable-full fs-sm">
                 <thead>
                     <tr>
                         <th>Nom et Prénom</th>
                         <th class="d-none d-sm-table-cell">Parcours</th>
                         <th class="d-none d-sm-table-cell">Niveau</th>
                         <th class="d-none d-sm-table-cell">Année Universitaire</th>
                         <th class="text-center" style="width: 120px;">Actions</th>
                     </tr>
                 </thead>
                 <tbody>

                     <?php $__currentLoopData = $etus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $etu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                         <tr>
                             <td class="fw-semibold">
                                 <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>

                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 <?php if($etu->parcours == null): ?>
                                     Parcours non ajouté
                                 <?php else: ?>
                                     <?php echo e($etu->parcours->sigle); ?>

                                 <?php endif; ?>
                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 <?php echo e($etu->niveau->nom); ?>

                             </td>
                             <td class="text-muted d-none d-sm-table-cell">
                                 <?php echo e($etu->annee->nom); ?>

                             </td>
                             
                             <td class="text-center">
                                 <div class="btn-group mb-2">
                                     <a href="<?php echo e(route('caf.caisse.payment.etudiant', ['userId' => $etu->user->id, 'niveauId' => $etu->niveau->id, 'anneeId' => $etu->annee->id])); ?>"
                                         class="btn btn-sm btn-alt-secondary" target="_blank">
                                         <i class="si si-wallet"></i> Payment
                                     </a>
                                 </div>

                             </td>
                         </tr>
                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     <?php echo e($etus->links()); ?>

                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
<?php /**PATH C:\xampp\htdocs\ImsaaFoad\resources\views/livewire/caf/payment/liste.blade.php ENDPATH**/ ?>