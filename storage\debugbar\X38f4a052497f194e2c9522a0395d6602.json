{"__meta": {"id": "X38f4a052497f194e2c9522a0395d6602", "datetime": "2025-07-01 17:40:51", "utime": 1751380851.092434, "method": "POST", "uri": "/livewire/upload-file?expires=1751381149&signature=1ebe7ade18f4a67766f9cc245ed47155aa470775a92d768951bcc1e4cd910e0e", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380850.277635, "end": 1751380851.092488, "duration": 0.8148529529571533, "duration_str": "815ms", "measures": [{"label": "Booting", "start": 1751380850.277635, "relative_start": 0, "end": 1751380850.843966, "relative_end": 1751380850.843966, "duration": 0.5663309097290039, "duration_str": "566ms", "params": [], "collector": null}, {"label": "Application", "start": 1751380850.844916, "relative_start": 0.5672810077667236, "end": 1751380851.092491, "relative_end": 2.86102294921875e-06, "duration": 0.2475748062133789, "duration_str": "248ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26436192, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/upload-file", "controller": "Livewire\\Controllers\\FileUploadHandler@handle", "as": "livewire.upload-file", "middleware": "web", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php&line=19\">\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php:19-28</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:16 [\n    \"sexe\" => \"H\"\n    \"nom\" => \"Rama\"\n    \"prenom\" => \"<PERSON>\"\n    \"date_naissance\" => \"1993-02-18\"\n    \"lieu_naissance\" => \"Ad et veniam exerci\"\n    \"adresse\" => \"Facere dicta ratione\"\n    \"nationalite\" => \"Sed odio quo sit ea \"\n    \"telephone2\" => \"1234123\"\n    \"cin\" => \"+****************\"\n    \"date_delivrance\" => \"1995-02-23\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261325551650\"\n    \"reference\" => \"124\"\n    \"telenvoi\" => \"1234123\"\n    \"montantenvoi\" => \"1233\"\n  ]\n  \"niveau\" => \"1\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n]"}, "request": {"path_info": "/livewire/upload-file", "status_code": "<pre class=sf-dump id=sf-dump-1300638964 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1300638964\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-614861963 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1751381149</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">1ebe7ade18f4a67766f9cc245ed47155aa470775a92d768951bcc1e4cd910e0e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614861963\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1701053092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1701053092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1230534155 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">528049</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarym6auQLSHBc2VRs2L</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Imk4Zk1CVjQxWmtiTlJQQmlyQmtTWEE9PSIsInZhbHVlIjoieEExdmI0YzdCSGNWTFVTZkFFaTFxSThBbzJROVJmc2lWTW1NSy9aNDhrMEJEaWJWOWdOOHc0bU16VVpaeHpTemg2ZGtjZ2ZaL1g3MXplQ1l3akxFUzJMdkdja2VSMjhuZVgvM2tyVjhIb1NvV2ExS2YxNnp4UkY2aVFlVVRGQysiLCJtYWMiOiIyYzg5ODAxZGY5ZWNiNDFiNDk2ZmU2NDBlMWY3ZmQ5ZGMyYjNmYWQ0NWEzMzQ2MmFlOTYyOWU3ZGUxMzg4Y2I0IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImxRNzZBY2RJWVJ4bU5hOWp3MnNxNVE9PSIsInZhbHVlIjoiT2tUNXBCK2tNVkJtZ0JGNUhDUS9kckZCL0RaMmRWbTBXWi8xTkdmaTJkOEliSWpoWGJmMnVqZnNnVDhXWGJlbGZmV0tLenExLzlZTmwyczAxWjhucHcxcld1S3JEdDRtblNTQWxqSWl5UHFuZjJYWWtUWU5xZkVNMGl5N2JsVngiLCJtYWMiOiI4ZWI0OTdkNTc2M2EzNjNlM2ViOGE1M2E4N2VmMDQwZjM0M2E5OGU3ZDZhMDBiYTJiMTllMWM0NjI4MDM1NDE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230534155\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1805545659 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58104</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"115 characters\">/livewire/upload-file?expires=1751381149&amp;signature=1ebe7ade18f4a67766f9cc245ed47155aa470775a92d768951bcc1e4cd910e0e</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"93 characters\">expires=1751381149&amp;signature=1ebe7ade18f4a67766f9cc245ed47155aa470775a92d768951bcc1e4cd910e0e</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"6 characters\">528049</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"6 characters\">528049</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarym6auQLSHBc2VRs2L</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarym6auQLSHBc2VRs2L</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Imk4Zk1CVjQxWmtiTlJQQmlyQmtTWEE9PSIsInZhbHVlIjoieEExdmI0YzdCSGNWTFVTZkFFaTFxSThBbzJROVJmc2lWTW1NSy9aNDhrMEJEaWJWOWdOOHc0bU16VVpaeHpTemg2ZGtjZ2ZaL1g3MXplQ1l3akxFUzJMdkdja2VSMjhuZVgvM2tyVjhIb1NvV2ExS2YxNnp4UkY2aVFlVVRGQysiLCJtYWMiOiIyYzg5ODAxZGY5ZWNiNDFiNDk2ZmU2NDBlMWY3ZmQ5ZGMyYjNmYWQ0NWEzMzQ2MmFlOTYyOWU3ZGUxMzg4Y2I0IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImxRNzZBY2RJWVJ4bU5hOWp3MnNxNVE9PSIsInZhbHVlIjoiT2tUNXBCK2tNVkJtZ0JGNUhDUS9kckZCL0RaMmRWbTBXWi8xTkdmaTJkOEliSWpoWGJmMnVqZnNnVDhXWGJlbGZmV0tLenExLzlZTmwyczAxWjhucHcxcld1S3JEdDRtblNTQWxqSWl5UHFuZjJYWWtUWU5xZkVNMGl5N2JsVngiLCJtYWMiOiI4ZWI0OTdkNTc2M2EzNjNlM2ViOGE1M2E4N2VmMDQwZjM0M2E5OGU3ZDZhMDBiYTJiMTllMWM0NjI4MDM1NDE2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380850.2776</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380850</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805545659\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1867431729 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yVCdhQsriTuZTEQBdHuoxzYc5BgY1Mo8vp1abE4A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867431729\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1356641314 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:40:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InVCUWsvNmd3eEVxTUJaR0FaZFZOeHc9PSIsInZhbHVlIjoiWGd6eEQwN2NETDAxaVgxcSt6QXlreDA2Ynl2Z2hvbC9VNEhzTEVPcThqTXFCYU1Zbnh6RFpDVWhyeVd5U2hsMTRKOEZKSzdiczlNV2hlaFAxVDVFemgybFYxRFBuTzVYUFFKY2ZkdlJOYWJsZlA0R21RTUs1bGRQTTcwSUNHVmYiLCJtYWMiOiI0YzcwZWJiZTU0MmE3NTMyZWIzMjZhNTA0MTNkZjk5NjAzZWY0NmVhOWUxOWFlZDg5NjU5NmI0MTAyZmY1Zjk4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:40:51 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6ImlRNlo5d3FVSHpzWUs2Z0xSUTQxZWc9PSIsInZhbHVlIjoiOTZDL0x2a1FnSDd6elZJdlJwOFhENUltQ1V3SVVJbEVSNUY3K2ZnNjZPbnpTdC9BSnNBRDJ1V2NTZ1FoVWgzZGpLa2dUMG12TThVSkxsNkg5QjZNS1FlYjh3Q3dtVWkwWEl6YU1WVkhNRy9scUN1bjBVd0RoeURvMlBTYlkwWGciLCJtYWMiOiJlNmFmNzg1MDI2Y2NkYmJhOTUzM2U2NTY4MDlkNzlhMjNlYzQxYzJlMGZkMjhjNmUwOTFjMDViZDNkOWNlMzVkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:40:51 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InVCUWsvNmd3eEVxTUJaR0FaZFZOeHc9PSIsInZhbHVlIjoiWGd6eEQwN2NETDAxaVgxcSt6QXlreDA2Ynl2Z2hvbC9VNEhzTEVPcThqTXFCYU1Zbnh6RFpDVWhyeVd5U2hsMTRKOEZKSzdiczlNV2hlaFAxVDVFemgybFYxRFBuTzVYUFFKY2ZkdlJOYWJsZlA0R21RTUs1bGRQTTcwSUNHVmYiLCJtYWMiOiI0YzcwZWJiZTU0MmE3NTMyZWIzMjZhNTA0MTNkZjk5NjAzZWY0NmVhOWUxOWFlZDg5NjU5NmI0MTAyZmY1Zjk4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:40:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6ImlRNlo5d3FVSHpzWUs2Z0xSUTQxZWc9PSIsInZhbHVlIjoiOTZDL0x2a1FnSDd6elZJdlJwOFhENUltQ1V3SVVJbEVSNUY3K2ZnNjZPbnpTdC9BSnNBRDJ1V2NTZ1FoVWgzZGpLa2dUMG12TThVSkxsNkg5QjZNS1FlYjh3Q3dtVWkwWEl6YU1WVkhNRy9scUN1bjBVd0RoeURvMlBTYlkwWGciLCJtYWMiOiJlNmFmNzg1MDI2Y2NkYmJhOTUzM2U2NTY4MDlkNzlhMjNlYzQxYzJlMGZkMjhjNmUwOTFjMDViZDNkOWNlMzVkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:40:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356641314\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1998876425 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:16</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rama</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Dyer</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1993-02-18</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Ad et veniam exerci</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Facere dicta ratione</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed odio quo sit ea </span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-02-23</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+261325551650</span>\"\n      \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"3 characters\">124</span>\"\n      \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n      \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1233</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998876425\", {\"maxDepth\":0})</script>\n"}}