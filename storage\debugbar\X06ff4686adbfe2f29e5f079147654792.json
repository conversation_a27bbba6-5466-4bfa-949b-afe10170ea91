{"__meta": {"id": "X06ff4686adbfe2f29e5f079147654792", "datetime": "2025-07-01 17:32:27", "utime": 1751380347.772783, "method": "GET", "uri": "/habilitations/utilisateurs?filterRole=1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380344.486657, "end": 1751380347.772839, "duration": 3.286182165145874, "duration_str": "3.29s", "measures": [{"label": "Booting", "start": 1751380344.486657, "relative_start": 0, "end": 1751380345.701254, "relative_end": 1751380345.701254, "duration": 1.2145969867706299, "duration_str": "1.21s", "params": [], "collector": null}, {"label": "Application", "start": 1751380345.702846, "relative_start": 1.2161891460418701, "end": 1751380347.772842, "relative_end": 2.86102294921875e-06, "duration": 2.069995880126953, "duration_str": "2.07s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27140480, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php (\\vendor\\livewire\\livewire\\src\\Macros\\livewire-view-extends.blade.php)", "param_count": 4, "params": ["view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php&line=0"}, {"name": "livewire.utilisateurs.index (\\resources\\views\\livewire\\utilisateurs\\index.blade.php)", "param_count": 33, "params": ["users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "errors", "_instance", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "showInscriptionModal", "selectedUserForInscription", "newInscription", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/index.blade.php&line=0"}, {"name": "livewire.utilisateurs.liste (\\resources\\views\\livewire\\utilisateurs\\liste.blade.php)", "param_count": 35, "params": ["__env", "app", "errors", "_instance", "users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "showInscriptionModal", "selectedUserForInscription", "newInscription", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/liste.blade.php&line=0"}, {"name": "layouts.backend (\\resources\\views\\layouts\\backend.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/backend.blade.php&line=0"}, {"name": "components.menu (\\resources\\views\\components\\menu.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/components/menu.blade.php&line=0"}, {"name": "components.rightHeader (\\resources\\views\\components\\rightHeader.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/components/rightHeader.blade.php&line=0"}]}, "route": {"uri": "GET habilitations/utilisateurs", "middleware": "web, auth, auth.admin", "controller": "App\\Http\\Livewire\\Utilisateurs@__invoke", "as": "admin.habilitations.users.index", "namespace": null, "prefix": "/habilitations", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Component.php&line=46\">\\vendor\\livewire\\livewire\\src\\Component.php:46-88</a>"}, "queries": {"nb_statements": 15, "nb_failed_statements": 0, "accumulated_duration": 0.12663000000000002, "accumulated_duration_str": "127ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0313, "duration_str": "31.3ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 24.718}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00316, "duration_str": "3.16ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 24.718, "width_percent": 2.495}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `roles`.`id` = 1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0025800000000000003, "duration_str": "2.58ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 27.213, "width_percent": 2.037}, {"sql": "select `id`, `nom`, `prenom`, `email`, `telephone1`, `photo`, `matricule`, `created_at`, `updated_at` from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `roles`.`id` = 1) and `users`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0025, "duration_str": "2.5ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 29.251, "width_percent": 1.974}, {"sql": "select `roles`.`id`, `roles`.`nom`, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (1, 17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 31.225, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 367}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 397}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 419}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 366}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 345}], "duration": 0.00181, "duration_str": "1.81ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:367", "connection": "inscriptionimsaa", "start_percent": 32.054, "width_percent": 1.429}, {"sql": "select count(*) as aggregate from `users` where `email` is not null and `password` is not null and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 368}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 397}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 419}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 366}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 345}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:368", "connection": "inscriptionimsaa", "start_percent": 33.483, "width_percent": 1.113}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and (`nom` like '%étudiant%' or `nom` like '%Etudiant%')) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%&eacute;tudiant%", "%Etudiant%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 371}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 397}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 419}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 366}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 345}], "duration": 0.00265, "duration_str": "2.65ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:371", "connection": "inscriptionimsaa", "start_percent": 34.597, "width_percent": 2.093}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.01139, "duration_str": "11.39ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 36.69, "width_percent": 8.995}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0055, "duration_str": "5.5ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 45.684, "width_percent": 4.343}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.05189, "duration_str": "51.89ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 50.028, "width_percent": 40.978}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00163, "duration_str": "1.63ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 91.005, "width_percent": 1.287}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 92.293, "width_percent": 0.671}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0069900000000000006, "duration_str": "6.99ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 92.964, "width_percent": 5.52}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\helpers.php", "line": 18}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "duration": 0.0019199999999999998, "duration_str": "1.92ms", "stmt_id": "\\app\\Helpers\\helpers.php:18", "connection": "inscriptionimsaa", "start_percent": 98.484, "width_percent": 1.516}]}, "models": {"data": {"App\\Models\\Role": 10, "App\\Models\\User": 3}, "count": 13}, "livewire": {"data": {"utilisateurs #wwmdBmkztc8xsrV2ZHYR": "array:5 [\n  \"data\" => array:20 [\n    \"currentPage\" => \"liste\"\n    \"newUser\" => []\n    \"editUser\" => []\n    \"mediaItems\" => []\n    \"isEtu\" => false\n    \"query\" => \"\"\n    \"newPasswd\" => null\n    \"filterRole\" => 1\n    \"filterStatus\" => \"\"\n    \"perPage\" => 10\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"selectedUsers\" => []\n    \"selectAll\" => false\n    \"rolePermissions\" => []\n    \"showInscriptionModal\" => false\n    \"selectedUserForInscription\" => []\n    \"newInscription\" => array:3 [\n      \"annee_universitaire_id\" => \"\"\n      \"niveau_id\" => \"\"\n      \"parcour_id\" => \"\"\n    ]\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"utilisateurs\"\n  \"view\" => \"livewire.utilisateurs.index\"\n  \"component\" => \"App\\Http\\Livewire\\Utilisateurs\"\n  \"id\" => \"wwmdBmkztc8xsrV2ZHYR\"\n]"}, "count": 1}, "gate": {"count": 7, "messages": [{"message": "[ability => admin, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1212432320 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212432320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380346.007612}, {"message": "[ability => admin, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1671198098 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1671198098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380347.618944}, {"message": "[ability => enseignant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1756219712 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756219712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380347.632949}, {"message": "[ability => deraq, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-933434201 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933434201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380347.691364}, {"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-501648044 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501648044\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380347.698673}, {"message": "[ability => secretaire, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-686192382 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686192382\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380347.708191}, {"message": "[ability => etudiant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-766498716 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766498716\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380347.722346}]}, "session": {"_token": "fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/habilitations/utilisateurs?filterRole=1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751377855\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/habilitations/utilisateurs", "status_code": "<pre class=sf-dump id=sf-dump-565147977 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-565147977\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-622429007 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>filterRole</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622429007\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1723277201 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1723277201\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-261419796 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik91eTc4SWJLVHh4emVUelJUU1haTkE9PSIsInZhbHVlIjoiWmdHTkVuVFM1STVGOGsrQ0MrRXh5YWhnWk5OQlJnaXEzOG5pNThSNEhyTEkrVkFUc2VNQWZvRk1semI3cG5XMlFiU1ExcFdBSHhtNXV4SVpCWXFWclE4elIxUkNsTE5UaWE2cHNRV3RFQUxFT0I2c0JrUnd3T2JlM2I4NW5sNXYiLCJtYWMiOiI5ZGUxZjY4NjlkMGYyODgxZjVkMWZhYWE1Yjk1ZDhjNjA3Yzc4NTYwZGJhMTU5NDEwN2RjNzUyNDBlOGY0MTZiIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImsyNDNPdHl1MlowWEhyVU5RUURwcFE9PSIsInZhbHVlIjoiYlZESmxubmlHTU1VQTArTVJMVkd3Ymgvc2pERXZJWDJnWnc4dTVuZmZiclhTYm4yMSsvMklMdHg4NTVnL01TM254VDlab3dXSnNnNWdtL05TOWU0S1J2S1hKczZuVnd1aHVJS2hkTStoVldPa3pYalpnM1R5Sy91b0lVaGR2UkkiLCJtYWMiOiIyZjM5NjNlNWVmMWEwNDU5NWJlOWRkZTMwZDZiMTM3ZjJlMDUwYjE3Y2ViZGEzZTk4Mzg5NTUwODViYjlhZjlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261419796\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-627219037 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57326</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/habilitations/utilisateurs?filterRole=1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/habilitations/utilisateurs</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/index.php/habilitations/utilisateurs</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"12 characters\">filterRole=1</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik91eTc4SWJLVHh4emVUelJUU1haTkE9PSIsInZhbHVlIjoiWmdHTkVuVFM1STVGOGsrQ0MrRXh5YWhnWk5OQlJnaXEzOG5pNThSNEhyTEkrVkFUc2VNQWZvRk1semI3cG5XMlFiU1ExcFdBSHhtNXV4SVpCWXFWclE4elIxUkNsTE5UaWE2cHNRV3RFQUxFT0I2c0JrUnd3T2JlM2I4NW5sNXYiLCJtYWMiOiI5ZGUxZjY4NjlkMGYyODgxZjVkMWZhYWE1Yjk1ZDhjNjA3Yzc4NTYwZGJhMTU5NDEwN2RjNzUyNDBlOGY0MTZiIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImsyNDNPdHl1MlowWEhyVU5RUURwcFE9PSIsInZhbHVlIjoiYlZESmxubmlHTU1VQTArTVJMVkd3Ymgvc2pERXZJWDJnWnc4dTVuZmZiclhTYm4yMSsvMklMdHg4NTVnL01TM254VDlab3dXSnNnNWdtL05TOWU0S1J2S1hKczZuVnd1aHVJS2hkTStoVldPa3pYalpnM1R5Sy91b0lVaGR2UkkiLCJtYWMiOiIyZjM5NjNlNWVmMWEwNDU5NWJlOWRkZTMwZDZiMTM3ZjJlMDUwYjE3Y2ViZGEzZTk4Mzg5NTUwODViYjlhZjlkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380344.4867</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380344</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627219037\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1706880111 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3fVPC4SnbhmIL9JnY36c5Wf3bMPv5WOAm4KXhpdT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706880111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-131175785 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:32:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNMS2dyTit5bUwva1orcjZKVlFrS2c9PSIsInZhbHVlIjoiSldmN2xQcXBwK0RkMmk3R3BWTWkwQXQ1Mk5tYVVKWWZqVnN1eVBEWlFkeHI3RGYwTXR5NGRTNmtCUHZtbXRGNG5Ya3BrRldHU1ArZTVLSndrYjBXRFd1Vm5FcHQwS0xiWlVzWWJHYUlndGNRU2FUdVVBNWlySHEzOXdqdkNqUEciLCJtYWMiOiJkOTk2Yjg2OTUzMDg4ZTIzZmY1MGM4ODQ1NTk5YjgwYjk5YjlmZDI1Y2UxYzU1YjdjY2U1ZDZkYmMwYWZkODg4IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:32:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IjF0S2J6WGl4K2ExWmFjZWhsd2RqdFE9PSIsInZhbHVlIjoiV2VkOHZteFg2emp4WkhEMCtiYW05a3NjaWtaeEg1R1VncEVNRElJbkZUQmNRc3ZKVHJnb0M4OUtqbFNqSGY0MEJOa3lOT1pqOWxiaUNCZjZuS3UwQ01HZUliQVZxNjNmQldvUGZuOC9ieHZUU1pWSWJyVmgzZXFWSHUvT0ZIbE4iLCJtYWMiOiI5MWRjNTQ5OTg2OTdjZTY4MDc1YzgxZDU0YWE3ZjVjMGU2MjUzNzFjMjgyODI0YjVhMDNlMzc5NmNiMmEyOTk0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:32:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNMS2dyTit5bUwva1orcjZKVlFrS2c9PSIsInZhbHVlIjoiSldmN2xQcXBwK0RkMmk3R3BWTWkwQXQ1Mk5tYVVKWWZqVnN1eVBEWlFkeHI3RGYwTXR5NGRTNmtCUHZtbXRGNG5Ya3BrRldHU1ArZTVLSndrYjBXRFd1Vm5FcHQwS0xiWlVzWWJHYUlndGNRU2FUdVVBNWlySHEzOXdqdkNqUEciLCJtYWMiOiJkOTk2Yjg2OTUzMDg4ZTIzZmY1MGM4ODQ1NTk5YjgwYjk5YjlmZDI1Y2UxYzU1YjdjY2U1ZDZkYmMwYWZkODg4IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:32:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IjF0S2J6WGl4K2ExWmFjZWhsd2RqdFE9PSIsInZhbHVlIjoiV2VkOHZteFg2emp4WkhEMCtiYW05a3NjaWtaeEg1R1VncEVNRElJbkZUQmNRc3ZKVHJnb0M4OUtqbFNqSGY0MEJOa3lOT1pqOWxiaUNCZjZuS3UwQ01HZUliQVZxNjNmQldvUGZuOC9ieHZUU1pWSWJyVmgzZXFWSHUvT0ZIbE4iLCJtYWMiOiI5MWRjNTQ5OTg2OTdjZTY4MDc1YzgxZDU0YWE3ZjVjMGU2MjUzNzFjMjgyODI0YjVhMDNlMzc5NmNiMmEyOTk0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:32:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131175785\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-536138670 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?filterRole=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751377855</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536138670\", {\"maxDepth\":0})</script>\n"}}