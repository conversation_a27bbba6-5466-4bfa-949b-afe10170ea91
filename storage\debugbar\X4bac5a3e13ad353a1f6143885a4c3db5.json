{"__meta": {"id": "X4bac5a3e13ad353a1f6143885a4c3db5", "datetime": "2025-07-01 18:20:03", "utime": 1751383203.928292, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751383196.151314, "end": 1751383203.928325, "duration": 7.777010917663574, "duration_str": "7.78s", "measures": [{"label": "Booting", "start": 1751383196.151314, "relative_start": 0, "end": 1751383197.7793, "relative_end": 1751383197.7793, "duration": 1.627985954284668, "duration_str": "1.63s", "params": [], "collector": null}, {"label": "Application", "start": 1751383197.782054, "relative_start": 1.6307399272918701, "end": 1751383203.928329, "relative_end": 4.0531158447265625e-06, "duration": 6.146275043487549, "duration_str": "6.15s", "params": [], "collector": null}]}, "memory": {"peak_usage": 25873456, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "foadregister (\\resources\\views\\foadregister.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/foadregister.blade.php&line=0"}, {"name": "livewire.multi-step-register (\\resources\\views\\livewire\\multi-step-register.blade.php)", "param_count": 17, "params": ["errors", "_instance", "newUser", "niveau", "parcour", "parcours", "cv", "diplome", "releveb<PERSON><PERSON>", "releve1", "releve2", "releve3", "releve4", "terms", "iteration", "currentStep", "previewDocument"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/multi-step-register.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 4, "params": ["__env", "app", "errors", "html"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "\\Illuminate\\Routing\\ViewController@__invoke", "controller": "\\Illuminate\\Routing\\ViewController", "namespace": null, "prefix": "", "where": [], "as": "register"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": {"multi-step-register #jEC3nyDduoNea0HbQ9Py": "array:5 [\n  \"data\" => array:15 [\n    \"newUser\" => []\n    \"niveau\" => null\n    \"parcour\" => null\n    \"parcours\" => Illuminate\\Support\\Collection {#758\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"cv\" => null\n    \"diplome\" => null\n    \"relevebacc\" => null\n    \"releve1\" => null\n    \"releve2\" => null\n    \"releve3\" => null\n    \"releve4\" => null\n    \"terms\" => null\n    \"iteration\" => null\n    \"currentStep\" => \"parcours\"\n    \"previewDocument\" => null\n  ]\n  \"name\" => \"multi-step-register\"\n  \"view\" => \"livewire.multi-step-register\"\n  \"component\" => \"App\\Http\\Livewire\\MultiStepRegister\"\n  \"id\" => \"jEC3nyDduoNea0HbQ9Py\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-378633568 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-378633568\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-386291476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-386291476\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1057666292 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1057666292\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-311428148 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkV2M2Q0eVNoV0cxT0ZZYkJacWZFVWc9PSIsInZhbHVlIjoiZC9NN0g3YUEzOE1uNUZGQWdFYVZBZmI4RHlqL1NyWkVJYVJLdUNtcW8yRDMrdldHVmZDSmZpSVFTaGtQcXdCa3czL1dmajBTRnhkS2Z6OEt6WWlNTWFsZlVabnhrWWsyVWk0dFdLMGlNc2ZHOFYwWFZKN05YZmFvMk9NZjNhUHAiLCJtYWMiOiIzYzBjZDdjZjlkNzhkMzYxYzZmZmQ5ZTEyYWRiYzAzZTQwMDZiZGE3MjA5NmViMDQwYWY1MTFlZDlmZDYwM2M4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImhsSGxPaEcyaWNsdW5wSktYckNJY2c9PSIsInZhbHVlIjoidGpoYzRCcXIyWVdldjhLa2VycUV5eUhvSjMvcFVDUkdXdW1ySCtjNDBRVEs0MEo2R3pvWlkrOVFrb3NzT1prWkVINC8vZHR3aEU0VDV5V2k5S0pkWXdXSHJDdnBNVHJBenRYTTBZbk5UN0dNTC9OVElqNHpaMmZ0cXBxS1RyT0UiLCJtYWMiOiI1ZDYzN2VjMDA5MTBiZDU2MzFhM2JmYTgzNzRmOTE1OTE1MDU1YzM3MzY5ZmRiMmEyNmE3NjRiMWNlYmRkYTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311428148\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1800439971 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60200</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkV2M2Q0eVNoV0cxT0ZZYkJacWZFVWc9PSIsInZhbHVlIjoiZC9NN0g3YUEzOE1uNUZGQWdFYVZBZmI4RHlqL1NyWkVJYVJLdUNtcW8yRDMrdldHVmZDSmZpSVFTaGtQcXdCa3czL1dmajBTRnhkS2Z6OEt6WWlNTWFsZlVabnhrWWsyVWk0dFdLMGlNc2ZHOFYwWFZKN05YZmFvMk9NZjNhUHAiLCJtYWMiOiIzYzBjZDdjZjlkNzhkMzYxYzZmZmQ5ZTEyYWRiYzAzZTQwMDZiZGE3MjA5NmViMDQwYWY1MTFlZDlmZDYwM2M4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImhsSGxPaEcyaWNsdW5wSktYckNJY2c9PSIsInZhbHVlIjoidGpoYzRCcXIyWVdldjhLa2VycUV5eUhvSjMvcFVDUkdXdW1ySCtjNDBRVEs0MEo2R3pvWlkrOVFrb3NzT1prWkVINC8vZHR3aEU0VDV5V2k5S0pkWXdXSHJDdnBNVHJBenRYTTBZbk5UN0dNTC9OVElqNHpaMmZ0cXBxS1RyT0UiLCJtYWMiOiI1ZDYzN2VjMDA5MTBiZDU2MzFhM2JmYTgzNzRmOTE1OTE1MDU1YzM3MzY5ZmRiMmEyNmE3NjRiMWNlYmRkYTZkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751383196.1513</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751383196</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800439971\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-750232722 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3MQqMzZAJmil6GWNRTwZQGoMQJ2F3HRZ41OmdrAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750232722\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-840291006 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:19:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik1leVREck9QUU5yUWd6eElNYlp1K2c9PSIsInZhbHVlIjoiTUxwcUs3c2pHY05LTkxTcE1TS2FYUEk1bERNR2phQ29TTFY3dEZ1eUpOZ0ZDaGljVjJpbkNTRjhwMTNYZEdtUEJEY3p5TkZJdDZWekZyWi9xUCtQTUNnZ2F2OE9qNHp1SVF4NFhiR29RdmVyU2hSODkwUjhxc0QwdUd1b0g0STciLCJtYWMiOiI1ZjhhNDkwMmIyMTlkOTQxODBlYTMwZmIyZjJjMjhkMzg4Yjg4YWYzOGJhMzcxMTkzMmQzMmMxNTM1ZWZmNTMxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:20:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6Imsxa3l5N0lXMGx6UXdSeTRrQUtvZ0E9PSIsInZhbHVlIjoiUDdtSmw0dm5NSWN5SGtOM01iT0JhSjg2TGo2T0o0N01mVFAxUWlkdEdiek0vVTQyWE9NK29XWUpvT1h5Z2Jpd01hSUtCWnJ3QkNoTlJJMWprU3R2RUR1WUNQUmNsekppY1NTdWxpQnM4aS9VWlZrbzZVYjV2U1pva1d1ckVDK0IiLCJtYWMiOiIwMzRkMGJiZTI5YmFjNmJiNTgwNzkxMTc3Yjc0OTkwYzM1MmJmNmQ3NjBhOTc4ODc1MjNjODA0ZDBmMzY3M2I5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:20:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik1leVREck9QUU5yUWd6eElNYlp1K2c9PSIsInZhbHVlIjoiTUxwcUs3c2pHY05LTkxTcE1TS2FYUEk1bERNR2phQ29TTFY3dEZ1eUpOZ0ZDaGljVjJpbkNTRjhwMTNYZEdtUEJEY3p5TkZJdDZWekZyWi9xUCtQTUNnZ2F2OE9qNHp1SVF4NFhiR29RdmVyU2hSODkwUjhxc0QwdUd1b0g0STciLCJtYWMiOiI1ZjhhNDkwMmIyMTlkOTQxODBlYTMwZmIyZjJjMjhkMzg4Yjg4YWYzOGJhMzcxMTkzMmQzMmMxNTM1ZWZmNTMxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:20:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6Imsxa3l5N0lXMGx6UXdSeTRrQUtvZ0E9PSIsInZhbHVlIjoiUDdtSmw0dm5NSWN5SGtOM01iT0JhSjg2TGo2T0o0N01mVFAxUWlkdEdiek0vVTQyWE9NK29XWUpvT1h5Z2Jpd01hSUtCWnJ3QkNoTlJJMWprU3R2RUR1WUNQUmNsekppY1NTdWxpQnM4aS9VWlZrbzZVYjV2U1pva1d1ckVDK0IiLCJtYWMiOiIwMzRkMGJiZTI5YmFjNmJiNTgwNzkxMTc3Yjc0OTkwYzM1MmJmNmQ3NjBhOTc4ODc1MjNjODA0ZDBmMzY3M2I5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:20:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840291006\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1403228620 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403228620\", {\"maxDepth\":0})</script>\n"}}