{"__meta": {"id": "X0bf4aeec7a2efa711a410b232ac11e6c", "datetime": "2025-07-01 18:10:15", "utime": 1751382615.246485, "method": "GET", "uri": "/livewire/preview-file/D2zLvYg9ZpECKicFnFNF0jBk6HaOX5-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384412&signature=226d788593c3248f9708549b75caf63258ae65699d1e406de534b1c80277e063", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751382614.546867, "end": 1751382615.24652, "duration": 0.699653148651123, "duration_str": "700ms", "measures": [{"label": "Booting", "start": 1751382614.546867, "relative_start": 0, "end": 1751382615.125327, "relative_end": 1751382615.125327, "duration": 0.5784602165222168, "duration_str": "578ms", "params": [], "collector": null}, {"label": "Application", "start": 1751382615.126644, "relative_start": 0.5797770023345947, "end": 1751382615.246523, "relative_end": 2.86102294921875e-06, "duration": 0.11987900733947754, "duration_str": "120ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 24329392, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/preview-file/{filename}", "controller": "Livewire\\Controllers\\FilePreviewHandler@handle", "as": "livewire.preview-file", "middleware": "web", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\FilePreviewHandler.php&line=11\">\\vendor\\livewire\\livewire\\src\\Controllers\\FilePreviewHandler.php:11-19</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/livewire/preview-file/D2zLvYg9ZpECKicFnFNF0jBk6HaOX5-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384412&signature=226d788593c3248f9708549b75caf63258ae65699d1e406de534b1c80277e063\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:16 [\n    \"sexe\" => \"F\"\n    \"date_naissance\" => \"1995-08-07\"\n    \"lieu_naissance\" => \"In rerum recusandae\"\n    \"adresse\" => \"Voluptates ut tempor\"\n    \"nationalite\" => \"Sed aut odio volupta\"\n    \"telephone2\" => \"+18795584611\"\n    \"cin\" => \"123412341234\"\n    \"date_delivrance\" => \"2020-01-24\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"nom\" => \"Aphrodite\"\n    \"prenom\" => \"Macdonald\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261 32 55 516 50\"\n    \"reference\" => \"21324\"\n    \"telenvoi\" => \"123412\"\n    \"montantenvoi\" => \"12341234\"\n  ]\n  \"niveau\" => \"2\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n]"}, "request": {"path_info": "/livewire/preview-file/D2zLvYg9ZpECKicFnFNF0jBk6HaOX5-metaYWxpcmVuY2lhLnBuZw==-.png", "status_code": "<pre class=sf-dump id=sf-dump-1931914172 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1931914172\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1229534853 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1751384412</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">226d788593c3248f9708549b75caf63258ae65699d1e406de534b1c80277e063</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229534853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1247538464 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1247538464\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1241411215 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImN0elVIU0tkNlQrQjhETk1VNEhhQlE9PSIsInZhbHVlIjoiTjhrUXNweUtTTndGUkRyTU9GRFl6K0IwYnVwQnZ6clovZk92K2FBRzl1a0d2NnRXZndzcC9jWmFIOExONzFPM1pMSFdiZElFdmJ4NmRkak1wd1lyS2IwdFZoTzRyTlFOL0svQ0hxQ1NtYlo5a1A2L09WM29jbmpNNE91KzU2V1EiLCJtYWMiOiJiODRmMWY0NTAyMGQ4NDExOWViN2QzODc4OWVjMDE3MTQ2NTUxY2FmZGQ4ZjBkNjA1ZDNkNjA3NzBlOWRhYTVmIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImloeWdvVnNudWk3WGdZY2gwR3YwSHc9PSIsInZhbHVlIjoiZ2NXL3hpM09Dd2pyc1pTT1dnNWRYSDhKODJDcHNLTnlCNittT3hPbG5tSEdsMXpOV1hSK1RUZnZqUWtES01LekoybzZYemFWUVcyVGJBZkdMQ2liTmNjWFNGQ2Y3UnZpUWxXNTd6U1FyVUZXT2lDUmFVRVUzWllsRXZIMG1mcHoiLCJtYWMiOiI3YzYxODljMDBmNzBjMzY4NmQzMGU5YjEwOTE3NGRmOGE3MDMxZGY2ZTg5MzQwNWM3YjA2Mzg0ZTVhNjk5NTM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1241411215\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1631701713 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59697</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"177 characters\">/livewire/preview-file/D2zLvYg9ZpECKicFnFNF0jBk6HaOX5-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384412&amp;signature=226d788593c3248f9708549b75caf63258ae65699d1e406de534b1c80277e063</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"83 characters\">/livewire/preview-file/D2zLvYg9ZpECKicFnFNF0jBk6HaOX5-metaYWxpcmVuY2lhLnBuZw==-.png</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"108 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console/../resources/server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"83 characters\">/livewire/preview-file/D2zLvYg9ZpECKicFnFNF0jBk6HaOX5-metaYWxpcmVuY2lhLnBuZw==-.png</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"93 characters\">expires=1751384412&amp;signature=226d788593c3248f9708549b75caf63258ae65699d1e406de534b1c80277e063</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImN0elVIU0tkNlQrQjhETk1VNEhhQlE9PSIsInZhbHVlIjoiTjhrUXNweUtTTndGUkRyTU9GRFl6K0IwYnVwQnZ6clovZk92K2FBRzl1a0d2NnRXZndzcC9jWmFIOExONzFPM1pMSFdiZElFdmJ4NmRkak1wd1lyS2IwdFZoTzRyTlFOL0svQ0hxQ1NtYlo5a1A2L09WM29jbmpNNE91KzU2V1EiLCJtYWMiOiJiODRmMWY0NTAyMGQ4NDExOWViN2QzODc4OWVjMDE3MTQ2NTUxY2FmZGQ4ZjBkNjA1ZDNkNjA3NzBlOWRhYTVmIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImloeWdvVnNudWk3WGdZY2gwR3YwSHc9PSIsInZhbHVlIjoiZ2NXL3hpM09Dd2pyc1pTT1dnNWRYSDhKODJDcHNLTnlCNittT3hPbG5tSEdsMXpOV1hSK1RUZnZqUWtES01LekoybzZYemFWUVcyVGJBZkdMQ2liTmNjWFNGQ2Y3UnZpUWxXNTd6U1FyVUZXT2lDUmFVRVUzWllsRXZIMG1mcHoiLCJtYWMiOiI3YzYxODljMDBmNzBjMzY4NmQzMGU5YjEwOTE3NGRmOGE3MDMxZGY2ZTg5MzQwNWM3YjA2Mzg0ZTVhNjk5NTM1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751382614.5469</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751382614</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631701713\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1263306548 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hRF2mVuiU5BibRd5o4CekB6Fo0oqlULGaPW0Otn7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263306548\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1451434680 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">image/png; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 15:10:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:10:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:10:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">4171891</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlpJamc3NUNnRVFDNmRQMWRaRFVoTHc9PSIsInZhbHVlIjoiV1ZpNUhqYitVQ3BCbHlocjFJRXdZVE9LN29OZVlXT000NFZqdVRDa0xBdHh4YmxJcG1QOEV0eVcwZDQ4LzlraTNQZ2REeTNyRVF6V09ZUTVydU5La05ud1RnS2FPZndKUWM4YVB6MFVKU0dKYU1tQUNlTXFGbmFKcTJJbTBhQlEiLCJtYWMiOiJhMzcyMWRhMTI0M2Y0NDI0MjEwZmI5OGMzMDE2M2U4NmViNWM5MTIxYWQwMWE5NmFhZjZjYmM3ZGU2MjljM2EyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:10:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IkZwckVHTTdFT1Y0Mm0vL09JUHNrWlE9PSIsInZhbHVlIjoiK2dXSFFQMGFJWXQ2dnNoYWdXcWZkU1I5b3JzN0VVb2Niem9pSzR1K2d6a0pmNkxNb1JadVM3L1hXOGNGWmNITlFWeUhDUDFPQ1VadkFqL091MVNlRnZlell0SWJZeExoQW1VZ2ZsVVJCbmgxdDRUek0rdHI0Wk9VVHlieGU1d0EiLCJtYWMiOiIyMWJlNzA4MWE1YWEzZmZjNTY1MDkxYjdmZTNjZTVlNDVhMDFmZTlhYmIwN2I2NjBlM2MzNDJjMjU2ZTczNWQyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:10:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlpJamc3NUNnRVFDNmRQMWRaRFVoTHc9PSIsInZhbHVlIjoiV1ZpNUhqYitVQ3BCbHlocjFJRXdZVE9LN29OZVlXT000NFZqdVRDa0xBdHh4YmxJcG1QOEV0eVcwZDQ4LzlraTNQZ2REeTNyRVF6V09ZUTVydU5La05ud1RnS2FPZndKUWM4YVB6MFVKU0dKYU1tQUNlTXFGbmFKcTJJbTBhQlEiLCJtYWMiOiJhMzcyMWRhMTI0M2Y0NDI0MjEwZmI5OGMzMDE2M2U4NmViNWM5MTIxYWQwMWE5NmFhZjZjYmM3ZGU2MjljM2EyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:10:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IkZwckVHTTdFT1Y0Mm0vL09JUHNrWlE9PSIsInZhbHVlIjoiK2dXSFFQMGFJWXQ2dnNoYWdXcWZkU1I5b3JzN0VVb2Niem9pSzR1K2d6a0pmNkxNb1JadVM3L1hXOGNGWmNITlFWeUhDUDFPQ1VadkFqL091MVNlRnZlell0SWJZeExoQW1VZ2ZsVVJCbmgxdDRUek0rdHI0Wk9VVHlieGU1d0EiLCJtYWMiOiIyMWJlNzA4MWE1YWEzZmZjNTY1MDkxYjdmZTNjZTVlNDVhMDFmZTlhYmIwN2I2NjBlM2MzNDJjMjU2ZTczNWQyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:10:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451434680\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2035133216 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"198 characters\">http://localhost:8000/livewire/preview-file/D2zLvYg9ZpECKicFnFNF0jBk6HaOX5-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384412&amp;signature=226d788593c3248f9708549b75caf63258ae65699d1e406de534b1c80277e063</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:16</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-08-07</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">In rerum recusandae</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Voluptates ut tempor</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed aut odio volupta</span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+18795584611</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2020-01-24</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Aphrodite</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Macdonald</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"19 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n      \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"5 characters\">21324</span>\"\n      \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123412</span>\"\n      \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12341234</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>2</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035133216\", {\"maxDepth\":0})</script>\n"}}