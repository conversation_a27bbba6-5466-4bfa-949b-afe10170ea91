{"__meta": {"id": "X794651b132c21922af512e4fdacbea9c", "datetime": "2025-07-01 18:19:07", "utime": 1751383147.123099, "method": "GET", "uri": "/livewire/livewire.js?id=fe747446aa84856d8b66", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751383146.383025, "end": 1751383147.124589, "duration": 0.7415640354156494, "duration_str": "742ms", "measures": [{"label": "Booting", "start": 1751383146.383025, "relative_start": 0, "end": 1751383147.030972, "relative_end": 1751383147.030972, "duration": 0.6479470729827881, "duration_str": "648ms", "params": [], "collector": null}, {"label": "Application", "start": 1751383147.0329, "relative_start": 0.6498751640319824, "end": 1751383147.124594, "relative_end": 5.0067901611328125e-06, "duration": 0.09169387817382812, "duration_str": "91.69ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 22559024, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Controllers\\LivewireJavaScriptAssets@source", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\LivewireJavaScriptAssets.php&line=9\">\\vendor\\livewire\\livewire\\src\\Controllers\\LivewireJavaScriptAssets.php:9-12</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-1903045696 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1903045696\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1551370315 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">fe747446aa84856d8b66</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551370315\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1246913974 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1246913974\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-963564833 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkV2M2Q0eVNoV0cxT0ZZYkJacWZFVWc9PSIsInZhbHVlIjoiZC9NN0g3YUEzOE1uNUZGQWdFYVZBZmI4RHlqL1NyWkVJYVJLdUNtcW8yRDMrdldHVmZDSmZpSVFTaGtQcXdCa3czL1dmajBTRnhkS2Z6OEt6WWlNTWFsZlVabnhrWWsyVWk0dFdLMGlNc2ZHOFYwWFZKN05YZmFvMk9NZjNhUHAiLCJtYWMiOiIzYzBjZDdjZjlkNzhkMzYxYzZmZmQ5ZTEyYWRiYzAzZTQwMDZiZGE3MjA5NmViMDQwYWY1MTFlZDlmZDYwM2M4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImhsSGxPaEcyaWNsdW5wSktYckNJY2c9PSIsInZhbHVlIjoidGpoYzRCcXIyWVdldjhLa2VycUV5eUhvSjMvcFVDUkdXdW1ySCtjNDBRVEs0MEo2R3pvWlkrOVFrb3NzT1prWkVINC8vZHR3aEU0VDV5V2k5S0pkWXdXSHJDdnBNVHJBenRYTTBZbk5UN0dNTC9OVElqNHpaMmZ0cXBxS1RyT0UiLCJtYWMiOiI1ZDYzN2VjMDA5MTBiZDU2MzFhM2JmYTgzNzRmOTE1OTE1MDU1YzM3MzY5ZmRiMmEyNmE3NjRiMWNlYmRkYTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963564833\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1992834456 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60146</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"45 characters\">/livewire/livewire.js?id=fe747446aa84856d8b66</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/livewire.js</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"108 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console/../resources/server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/livewire.js</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">id=fe747446aa84856d8b66</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkV2M2Q0eVNoV0cxT0ZZYkJacWZFVWc9PSIsInZhbHVlIjoiZC9NN0g3YUEzOE1uNUZGQWdFYVZBZmI4RHlqL1NyWkVJYVJLdUNtcW8yRDMrdldHVmZDSmZpSVFTaGtQcXdCa3czL1dmajBTRnhkS2Z6OEt6WWlNTWFsZlVabnhrWWsyVWk0dFdLMGlNc2ZHOFYwWFZKN05YZmFvMk9NZjNhUHAiLCJtYWMiOiIzYzBjZDdjZjlkNzhkMzYxYzZmZmQ5ZTEyYWRiYzAzZTQwMDZiZGE3MjA5NmViMDQwYWY1MTFlZDlmZDYwM2M4IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImhsSGxPaEcyaWNsdW5wSktYckNJY2c9PSIsInZhbHVlIjoidGpoYzRCcXIyWVdldjhLa2VycUV5eUhvSjMvcFVDUkdXdW1ySCtjNDBRVEs0MEo2R3pvWlkrOVFrb3NzT1prWkVINC8vZHR3aEU0VDV5V2k5S0pkWXdXSHJDdnBNVHJBenRYTTBZbk5UN0dNTC9OVElqNHpaMmZ0cXBxS1RyT0UiLCJtYWMiOiI1ZDYzN2VjMDA5MTBiZDU2MzFhM2JmYTgzNzRmOTE1OTE1MDU1YzM3MzY5ZmRiMmEyNmE3NjRiMWNlYmRkYTZkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751383146.383</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751383146</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992834456\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1513438545 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkV2M2Q0eVNoV0cxT0ZZYkJacWZFVWc9PSIsInZhbHVlIjoiZC9NN0g3YUEzOE1uNUZGQWdFYVZBZmI4RHlqL1NyWkVJYVJLdUNtcW8yRDMrdldHVmZDSmZpSVFTaGtQcXdCa3czL1dmajBTRnhkS2Z6OEt6WWlNTWFsZlVabnhrWWsyVWk0dFdLMGlNc2ZHOFYwWFZKN05YZmFvMk9NZjNhUHAiLCJtYWMiOiIzYzBjZDdjZjlkNzhkMzYxYzZmZmQ5ZTEyYWRiYzAzZTQwMDZiZGE3MjA5NmViMDQwYWY1MTFlZDlmZDYwM2M4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImhsSGxPaEcyaWNsdW5wSktYckNJY2c9PSIsInZhbHVlIjoidGpoYzRCcXIyWVdldjhLa2VycUV5eUhvSjMvcFVDUkdXdW1ySCtjNDBRVEs0MEo2R3pvWlkrOVFrb3NzT1prWkVINC8vZHR3aEU0VDV5V2k5S0pkWXdXSHJDdnBNVHJBenRYTTBZbk5UN0dNTC9OVElqNHpaMmZ0cXBxS1RyT0UiLCJtYWMiOiI1ZDYzN2VjMDA5MTBiZDU2MzFhM2JmYTgzNzRmOTE1OTE1MDU1YzM3MzY5ZmRiMmEyNmE3NjRiMWNlYmRkYTZkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513438545\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1438685144 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 15:19:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 29 Jan 2023 23:45:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:19:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">174545</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438685144\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1171119046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1171119046\", {\"maxDepth\":0})</script>\n"}}