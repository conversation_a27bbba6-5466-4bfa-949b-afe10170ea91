<?php

namespace App\Http\Livewire;

use App\Mail\Inscription;
use App\Models\Mention;
use App\Models\Parcour;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Livewire\WithFileUploads;

class MultiStepRegister extends Component
{
    use WithFileUploads;
    public $newUser = [];
    public $niveau;
    public $parcour;
    public $parcours;
    public $cv;
    public $diplome;
    public $relevebacc;
    public $releve1;
    public $releve2;
    public $releve3;
    public $releve4;
    public $terms;
    public $iteration;
    public $currentStep = 'parcours';
    public $previewDocument = null;
    

    // protected $listeners = ["selectDate" => 'getSelectedDate', "selectDateCin" => 'getSelectedDateCin'];


    public function mount()
    {
        $this->parcours = collect();
        
        // Récupérer les données sauvegardées si elles existent
        if (session()->has('registration_data')) {
            $savedData = session('registration_data');
            $this->newUser = $savedData['newUser'] ?? [];
            $this->niveau = $savedData['niveau'] ?? null;
            $this->parcour = $savedData['parcour'] ?? null;
            $this->currentStep = $savedData['currentStep'] ?? 'parcours';
            
            // Recharger les parcours si le niveau est défini
            if ($this->niveau) {
                $this->parcours = Mention::whereRelation('niveau', 'niveau_id', '=', $this->niveau)->get();
            }
        }
    }


    public function render()
    {
        return view('livewire.multi-step-register');
    }

    public function updatedNiveau($value)
    {
        $this->parcours = Mention::whereRelation('niveau', 'niveau_id', '=', $value)->get();
        $this->parcour = $this->parcours->first()->id ?? null;
        $this->relevebacc = null;
        $this->cv = null;
        $this->diplome = null;
        $this->releve1 = null;
        $this->releve2 = null;
        $this->releve3 = null;
        $this->releve4 = null;
        $this->iteration++;
    }

    // public function getSelectedDate($date)
    // {
    //     $this->newUser["date_naissance"] = $date;
    // }

    // public function getSelectedDateCin($date)
    // {
    //     $this->newUser["date_delivrance"] = $date;
    // }

    public function getProgressPercentage()
    {
        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $completedSteps = 0;

        foreach ($steps as $step) {
            if ($this->isStepComplete($step)) {
                $completedSteps++;
            }
        }

        return ($completedSteps / count($steps)) * 100;
    }

    public function isStepComplete($step)
    {
        switch ($step) {
            case 'parcours':
                return !empty($this->niveau) && !empty($this->parcour);
            
            case 'informations':
                $requiredFields = [
                    'nom', 'prenom', 'email', 'telephone1', 'sexe', 
                    'date_naissance', 'lieu_naissance', 'nationalite', 'adresse'
                ];
                
                foreach ($requiredFields as $field) {
                    if (empty($this->newUser[$field])) {
                        return false;
                    }
                }
                
                // Validation spécifique pour l'email
                if (!filter_var($this->newUser['email'], FILTER_VALIDATE_EMAIL)) {
                    return false;
                }
                
                // Validation spécifique pour le téléphone
                if (!preg_match('/^\+261\s?\d{2}\s?\d{2}\s?\d{3}\s?\d{2}$/', $this->newUser['telephone1'])) {
                    return false;
                }
                
                return true;
            
            case 'documents':
                if ($this->niveau == 1) {
                    return !empty($this->relevebacc);
                } elseif ($this->niveau == 2) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve1);
                } elseif ($this->niveau == 3) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve2);
                } elseif ($this->niveau == 4) {
                    return !empty($this->cv) && !empty($this->diplome);
                } elseif ($this->niveau == 5) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve4);
                }
                return false;
            
            case 'paiement':
                $requiredFields = ['reference', 'telenvoi', 'montantenvoi'];
                foreach ($requiredFields as $field) {
                    if (empty($this->newUser[$field])) {
                        return false;
                    }
                }
                return true;
            
            default:
                return false;
        }
    }

    protected function getFileValidationRules()
    {
        return [
            'max:5120', // 5MB
            'mimes:pdf,jpg,jpeg,png',
        ];
    }

    public function rules()
    {
        $fileRules = $this->getFileValidationRules();
        
        if ($this->niveau == 1) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'relevebacc' => array_merge(['required'], $fileRules),
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.telenvoi' => 'required',
                'newUser.montantenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 2) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => array_merge(['required'], $fileRules),
                'diplome' => array_merge(['required'], $fileRules),
                'releve1' => array_merge(['required'], $fileRules),
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.telenvoi' => 'required',
                'newUser.montantenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 3) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                'releve2' => 'required',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 4) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                // 'releve1' => 'required',
                // 'releve2' => 'required',
                'releve3' => '',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } elseif ($this->niveau == 5) {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                // 'releve1' => 'required',
                // 'releve2' => 'required',
                'releve4' => 'required',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        } else {
            return [
                'newUser.nom' => 'required',
                'newUser.prenom' => '',
                'newUser.email' => 'required|email|unique:users,email',
                'newUser.sexe' => 'required',
                'newUser.date_naissance' => 'required|date',
                'newUser.lieu_naissance' => 'required',
                'newUser.nationalite' => 'required',
                'newUser.date_delivrance' => 'date',
                'newUser.lieu_delivrance' => '',
                'newUser.adresse' => 'required',
                'newUser.telephone1' => 'required',
                'newUser.telephone2' => '',
                'newUser.cin' => 'unique:users,cin',
                'niveau' => 'required',
                'cv' => 'required',
                'diplome' => 'required',
                'parcour' => 'required',
                'newUser.reference' => 'required',
                'newUser.montantenvoi' => 'required',
                'newUser.telenvoi' => 'required',
                'terms' => 'accepted'
            ];
        }
    }

    protected function getCustomMessages()
    {
        return [
            'relevebacc.max' => 'Le fichier ne doit pas dépasser 5MB',
            'relevebacc.mimes' => 'Le fichier doit être au format PDF, JPG, JPEG ou PNG',
            'cv.max' => 'Le CV ne doit pas dépasser 5MB',
            'cv.mimes' => 'Le CV doit être au format PDF, JPG, JPEG ou PNG',
            'diplome.max' => 'Le diplôme ne doit pas dépasser 5MB',
            'diplome.mimes' => 'Le diplôme doit être au format PDF, JPG, JPEG ou PNG',
            'releve1.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve1.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
            'releve2.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve2.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
            'releve3.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve3.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
            'releve4.max' => 'Le relevé ne doit pas dépasser 5MB',
            'releve4.mimes' => 'Le relevé doit être au format PDF, JPG, JPEG ou PNG',
        ];
    }

    public function validate($rules = null, $messages = [], $attributes = [])
    {
        return parent::validate($rules, array_merge($this->getCustomMessages(), $messages), $attributes);
    }

    public function register()
    {
        $this->emit('showLoading');
        $validationAttributes = $this->validate();
        
        // Nettoyer les données sauvegardées après l'inscription réussie
        session()->forget('registration_data');
        
        $validationAttributes["newUser"]["photo"] = "media/avatars/avatar0.jpg";
        $validationAttributes["newUser"]["mention_id"] = $this->parcour;
        $validationAttributes["newUser"]["niveau_id"] = $this->niveau;

        // dump($validationAttributes);
        // Ajouter un nouvel utilisateur


        if ($this->niveau == 1) {
            if ($validationAttributes["relevebacc"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["relevebacc"])->toMediaCollection('Releve_ou_Diplome_Bacc', 'private');
        } elseif ($this->niveau == 2) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null && $validationAttributes["releve1"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            $user->addMedia($validationAttributes["releve1"])->toMediaCollection('Releve_L1_ou_Attestation', 'private');
        } elseif ($this->niveau == 3) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null && $validationAttributes["releve2"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            $user->addMedia($validationAttributes["releve2"])->toMediaCollection('Releve_L2', 'private');
        } elseif ($this->niveau == 4) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            if ($validationAttributes["releve3"] != null) {
                $user->addMedia($validationAttributes["releve3"])->toMediaCollection('Releve_L3_ou_Attestation', 'private');
            }
        } elseif ($this->niveau == 5) {
            if ($validationAttributes["cv"] != null && $validationAttributes["diplome"] != null && $validationAttributes["releve4"] != null) {
                $user = User::create($validationAttributes["newUser"]);
                $user->roles()->attach(5);
            }
            $user->addMedia($validationAttributes["cv"])->toMediaCollection('CV', 'private');
            $user->addMedia($validationAttributes["diplome"])->toMediaCollection('Diplome', 'private');
            $user->addMedia($validationAttributes["releve4"])->toMediaCollection('Releve_L4', 'private');
        }

        Mail::to($this->newUser["email"])->send(new Inscription($this->newUser["nom"], $this->newUser["prenom"]));

        $data = ['name' => $this->newUser["nom"] . ' ' . $this->newUser["prenom"], 'email' => $this->newUser["email"]];
        $this->newUser = [];
        $this->emit('hideLoading');
        return redirect()->route('registration.success', $data);
    }

    public function removeFile($property)
    {
        if ($this->$property) {
            $this->$property = null;
            $this->iteration++;
        }
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);

        if (strpos($propertyName, 'newUser.') === 0) {
            $this->validateField($propertyName);
            $this->emit('fieldUpdated', $propertyName);
        }

        // Auto-format phone number
        if ($propertyName === 'newUser.telephone1' || $propertyName === 'newUser.telephone2') {
            $this->formatPhoneNumber($propertyName);
        }

        // Auto-suggest email
        if ($propertyName === 'newUser.nom' || $propertyName === 'newUser.prenom') {
            $this->emit('nameUpdated');
        }

        $this->saveToSession();
        $this->emit('progressUpdated', $this->getProgressPercentage());
    }

    protected function formatPhoneNumber($propertyName)
    {
        $field = str_replace('newUser.', '', $propertyName);
        $phone = $this->newUser[$field] ?? '';

        // Remove all non-digit characters except +
        $phone = preg_replace('/[^\d+]/', '', $phone);

        // Add +261 if not present and starts with 0
        if (preg_match('/^0\d{9}$/', $phone)) {
            $phone = '+261' . substr($phone, 1);
        }

        // Format the number
        if (preg_match('/^\+261(\d{2})(\d{2})(\d{3})(\d{2})$/', $phone, $matches)) {
            $phone = '+261 ' . $matches[1] . ' ' . $matches[2] . ' ' . $matches[3] . ' ' . $matches[4];
        }

        $this->newUser[$field] = $phone;
    }

    protected function saveToSession()
    {
        session()->put('registration_data', [
            'newUser' => $this->newUser,
            'niveau' => $this->niveau,
            'parcour' => $this->parcour,
            'currentStep' => $this->currentStep,
            // Sauvegarder les informations des fichiers (pas les fichiers eux-mêmes)
            'files_uploaded' => [
                'cv' => $this->cv ? true : false,
                'diplome' => $this->diplome ? true : false,
                'relevebacc' => $this->relevebacc ? true : false,
                'releve1' => $this->releve1 ? true : false,
                'releve2' => $this->releve2 ? true : false,
                'releve3' => $this->releve3 ? true : false,
                'releve4' => $this->releve4 ? true : false,
            ]
        ]);
    }

    public function goToStep($step)
    {
        if ($this->canAccessStep($step)) {
            $this->currentStep = $step;
            // Ne pas réinitialiser les fichiers lors de la navigation
            $this->iteration++;
            $this->saveToSession();
        }
    }

    public function previousStep()
    {
        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $currentIndex = array_search($this->currentStep, $steps);

        if ($currentIndex > 0) {
            $this->currentStep = $steps[$currentIndex - 1];
            // Ne pas réinitialiser les fichiers lors de la navigation
            $this->iteration++;
            $this->saveToSession();
        }
    }

    public function nextStep()
    {
        if (!$this->canProceedToNextStep()) {
            $this->addError('step', 'Veuillez remplir tous les champs obligatoires avant de continuer.');
            return;
        }

        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $currentIndex = array_search($this->currentStep, $steps);

        if ($currentIndex < count($steps) - 1) {
            $this->currentStep = $steps[$currentIndex + 1];
            // Ne pas réinitialiser les fichiers lors du passage à l'étape suivante
            $this->iteration++;
            $this->saveToSession();
        }
    }

    public function canAccessStep($step)
    {
        $steps = ['parcours', 'informations', 'documents', 'paiement'];
        $currentIndex = array_search($this->currentStep, $steps);
        $targetIndex = array_search($step, $steps);

        if ($targetIndex <= $currentIndex) {
            return true;
        }

        return $this->isStepComplete($this->currentStep);
    }

    public function canProceedToNextStep()
    {
        if (!$this->isStepComplete($this->currentStep)) {
            return false;
        }

        // Validation supplémentaire selon l'étape
        switch ($this->currentStep) {
            case 'parcours':
                return !empty($this->niveau) && !empty($this->parcour);
            
            case 'informations':
                return !empty($this->newUser['nom']) && 
                       !empty($this->newUser['prenom']) && 
                       !empty($this->newUser['email']) && 
                       !empty($this->newUser['telephone1']) &&
                       !empty($this->newUser['sexe']) &&
                       !empty($this->newUser['date_naissance']) &&
                       !empty($this->newUser['lieu_naissance']) &&
                       !empty($this->newUser['nationalite']) &&
                       !empty($this->newUser['adresse']);
            
            case 'documents':
                if ($this->niveau == 1) {
                    return !empty($this->relevebacc);
                } elseif ($this->niveau == 2) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve1);
                } elseif ($this->niveau == 3) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve2);
                } elseif ($this->niveau == 4) {
                    return !empty($this->cv) && !empty($this->diplome);
                } elseif ($this->niveau == 5) {
                    return !empty($this->cv) && !empty($this->diplome) && !empty($this->releve4);
                }
                return false;
            
            case 'paiement':
                return !empty($this->newUser['reference']) && 
                       !empty($this->newUser['telenvoi']) && 
                       !empty($this->newUser['montantenvoi']);
            
            default:
                return false;
        }
    }

    public function previewDocument($property)
    {
        if ($this->$property) {
            $this->previewDocument = $this->$property;
            $this->dispatchBrowserEvent('showDocumentPreview');
        }
    }

    protected function validateField($propertyName)
    {
        $field = str_replace('newUser.', '', $propertyName);
        $value = $this->newUser[$field] ?? null;

        // Clear previous errors for this field
        $this->resetErrorBag('newUser.' . $field);

        switch ($field) {
            case 'nom':
            case 'prenom':
                if ($value && strlen($value) < 2) {
                    $this->addError('newUser.' . $field, 'Doit contenir au moins 2 caractères');
                } elseif ($value && !preg_match('/^[a-zA-ZÀ-ÿ\s\-\']+$/u', $value)) {
                    $this->addError('newUser.' . $field, 'Caractères invalides détectés');
                }
                break;

            case 'email':
                if ($value) {
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $this->addError('newUser.' . $field, 'Format d\'email invalide');
                    } else {
                        // Check if email already exists (you might want to implement this)
                        $this->emit('emailValidated', $value);
                    }
                }
                break;

            case 'telephone1':
            case 'telephone2':
                if ($value) {
                    if (!preg_match('/^\+261\s?\d{2}\s?\d{2}\s?\d{3}\s?\d{2}$/', $value)) {
                        $this->addError('newUser.' . $field, 'Format de téléphone invalide (ex: +261 XX XX XXX XX)');
                    } else {
                        $this->emit('phoneValidated', $value);
                    }
                }
                break;

            case 'date_naissance':
                if ($value) {
                    try {
                        $date = \Carbon\Carbon::parse($value);
                        $age = $date->age;

                        if ($age < 16) {
                            $this->addError('newUser.' . $field, 'Vous devez avoir au moins 16 ans');
                        } elseif ($age > 80) {
                            $this->addError('newUser.' . $field, 'Veuillez vérifier votre date de naissance');
                        } else {
                            $this->emit('ageCalculated', $age);
                        }
                    } catch (\Exception $e) {
                        $this->addError('newUser.' . $field, 'Date invalide');
                    }
                }
                break;

            case 'cin':
                if ($value && !preg_match('/^\d{12}$/', $value)) {
                    $this->addError('newUser.' . $field, 'Le CIN doit contenir 12 chiffres');
                }
                break;

            case 'nationalite':
                if ($value && strlen($value) < 3) {
                    $this->addError('newUser.' . $field, 'Nationalité trop courte');
                }
                break;
        }
    }

    public function getFieldValidationStatus($field)
    {
        $value = $this->newUser[$field] ?? null;
        $hasError = $this->getErrorBag()->has('newUser.' . $field);

        if (empty($value)) {
            return 'empty';
        }

        if ($hasError) {
            return 'invalid';
        }

        return 'valid';
    }

    public function getStepCompletionDetails($step)
    {
        $details = [
            'completed' => $this->isStepComplete($step),
            'fields' => [],
            'progress' => 0
        ];

        switch ($step) {
            case 'parcours':
                $fields = ['niveau', 'parcour'];
                break;
            case 'informations':
                $fields = ['nom', 'prenom', 'email', 'telephone1', 'sexe', 'date_naissance', 'lieu_naissance', 'nationalite', 'adresse'];
                break;
            case 'documents':
                $fields = $this->getRequiredDocumentsForLevel();
                break;
            case 'paiement':
                $fields = ['reference', 'telenvoi', 'montantenvoi'];
                break;
            default:
                $fields = [];
        }

        $completedFields = 0;
        foreach ($fields as $field) {
            $status = $step === 'parcours' ?
                (!empty($this->$field) ? 'valid' : 'empty') :
                $this->getFieldValidationStatus($field);

            $details['fields'][$field] = $status;
            if ($status === 'valid') {
                $completedFields++;
            }
        }

        $details['progress'] = count($fields) > 0 ? ($completedFields / count($fields)) * 100 : 0;

        return $details;
    }

    protected function getRequiredDocumentsForLevel()
    {
        switch ($this->niveau) {
            case 1:
                return ['relevebacc'];
            case 2:
                return ['cv', 'diplome', 'releve1'];
            case 3:
                return ['cv', 'diplome', 'releve2'];
            case 4:
                return ['cv', 'diplome'];
            case 5:
                return ['cv', 'diplome', 'releve4'];
            default:
                return [];
        }
    }

    public function hasUploadedFile($property)
    {
        return !empty($this->$property);
    }

    public function getUploadedFileName($property)
    {
        if ($this->hasUploadedFile($property)) {
            return $this->$property->getClientOriginalName();
        }
        return null;
    }

    public function getUploadedFileSize($property)
    {
        if ($this->hasUploadedFile($property)) {
            return number_format($this->$property->getSize() / 1024, 1) . ' KB';
        }
        return null;
    }

    private function resetFileInputs()
    {
        $this->cv = null;
        $this->diplome = null;
        $this->relevebacc = null;
        $this->releve1 = null;
        $this->releve2 = null;
        $this->releve3 = null;
        $this->releve4 = null;
    }
}
