{"__meta": {"id": "Xafa2ff4dd87fe72e1f3bea89df7efa7f", "datetime": "2025-07-01 17:40:55", "utime": 1751380855.085897, "method": "POST", "uri": "/livewire/message/multi-step-register", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380854.570262, "end": 1751380855.085921, "duration": 0.5156590938568115, "duration_str": "516ms", "measures": [{"label": "Booting", "start": 1751380854.570262, "relative_start": 0, "end": 1751380854.948776, "relative_end": 1751380854.948776, "duration": 0.37851405143737793, "duration_str": "379ms", "params": [], "collector": null}, {"label": "Application", "start": 1751380854.949438, "relative_start": 0.37917613983154297, "end": 1751380855.085923, "relative_end": 1.9073486328125e-06, "duration": 0.13648486137390137, "duration_str": "136ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26060640, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.multi-step-register (\\resources\\views\\livewire\\multi-step-register.blade.php)", "param_count": 17, "params": ["errors", "_instance", "newUser", "niveau", "parcour", "parcours", "cv", "diplome", "releveb<PERSON><PERSON>", "releve1", "releve2", "releve3", "releve4", "terms", "iteration", "currentStep", "previewDocument"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/multi-step-register.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00382, "accumulated_duration_str": "3.82ms", "statements": [{"sql": "select * from `mentions` where `mentions`.`id` in (1, 2, 3, 4, 5, 6, 7, 11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00382, "duration_str": "3.82ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Mention": 9}, "count": 9}, "livewire": {"data": {"multi-step-register #1118lCXNZbYkEvQlZBHX": "array:5 [\n  \"data\" => array:15 [\n    \"newUser\" => array:16 [\n      \"sexe\" => \"H\"\n      \"nom\" => \"Rama\"\n      \"prenom\" => \"<PERSON>\"\n      \"date_naissance\" => \"1993-02-18\"\n      \"lieu_naissance\" => \"Ad et veniam exerci\"\n      \"adresse\" => \"Facere dicta ratione\"\n      \"nationalite\" => \"Sed odio quo sit ea \"\n      \"telephone2\" => \"1234123\"\n      \"cin\" => \"+****************\"\n      \"date_delivrance\" => \"1995-02-23\"\n      \"lieu_delivrance\" => \"+****************\"\n      \"email\" => \"<EMAIL>\"\n      \"telephone1\" => \"+261325551650\"\n      \"reference\" => \"124\"\n      \"telenvoi\" => \"1234123\"\n      \"montantenvoi\" => \"1233\"\n    ]\n    \"niveau\" => \"1\"\n    \"parcour\" => 1\n    \"parcours\" => Illuminate\\Database\\Eloquent\\Collection {#1702\n      #items: array:9 [\n        0 => App\\Models\\Mention {#1712\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 1\n            \"nom\" => \"Banques Et Assurances\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 1\n            \"nom\" => \"Banques Et Assurances\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Mention {#1710\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Mention {#1709\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 3\n            \"nom\" => \"Finance, Comptabilité et Audit\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 3\n            \"nom\" => \"Finance, Comptabilité et Audit\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Mention {#1708\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 4\n            \"nom\" => \"Tourisme hôtellerie\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 4\n            \"nom\" => \"Tourisme hôtellerie\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Mention {#1707\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Communication et Marketing\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Communication et Marketing\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Mention {#1706\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 6\n            \"nom\" => \"Génie Electrique et Informatique Industrielle\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 6\n            \"nom\" => \"Génie Electrique et Informatique Industrielle\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Mention {#1705\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 7\n            \"nom\" => \"Génie Civil\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 7\n            \"nom\" => \"Génie Civil\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Mention {#1703\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 11\n            \"nom\" => \"Génie Logiciel et Systèmes\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 11\n            \"nom\" => \"Génie Logiciel et Systèmes\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Mention {#1704\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 8\n            \"nom\" => \"Génie Mécanique\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 8\n            \"nom\" => \"Génie Mécanique\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"cv\" => null\n    \"diplome\" => null\n    \"relevebacc\" => null\n    \"releve1\" => null\n    \"releve2\" => null\n    \"releve3\" => null\n    \"releve4\" => null\n    \"terms\" => true\n    \"iteration\" => 10\n    \"currentStep\" => \"paiement\"\n    \"previewDocument\" => null\n  ]\n  \"name\" => \"multi-step-register\"\n  \"view\" => \"livewire.multi-step-register\"\n  \"component\" => \"App\\Http\\Livewire\\MultiStepRegister\"\n  \"id\" => \"1118lCXNZbYkEvQlZBHX\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:16 [\n    \"sexe\" => \"H\"\n    \"nom\" => \"Rama\"\n    \"prenom\" => \"<PERSON>\"\n    \"date_naissance\" => \"1993-02-18\"\n    \"lieu_naissance\" => \"Ad et veniam exerci\"\n    \"adresse\" => \"Facere dicta ratione\"\n    \"nationalite\" => \"Sed odio quo sit ea \"\n    \"telephone2\" => \"1234123\"\n    \"cin\" => \"+****************\"\n    \"date_delivrance\" => \"1995-02-23\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261325551650\"\n    \"reference\" => \"124\"\n    \"telenvoi\" => \"1234123\"\n    \"montantenvoi\" => \"1233\"\n  ]\n  \"niveau\" => \"1\"\n  \"parcour\" => 1\n  \"currentStep\" => \"paiement\"\n]"}, "request": {"path_info": "/livewire/message/multi-step-register", "status_code": "<pre class=sf-dump id=sf-dump-2007005944 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2007005944\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-111380434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111380434\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1373217636 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">1118lCXNZbYkEvQlZBHX</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">multi-step-register</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">ec7e8c15</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:16</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rama</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Dyer</span>\"\n        \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1993-02-18</span>\"\n        \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Ad et veniam exerci</span>\"\n        \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Facere dicta ratione</span>\"\n        \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed odio quo sit ea </span>\"\n        \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n        \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-02-23</span>\"\n        \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+261325551650</span>\"\n        \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"3 characters\">124</span>\"\n        \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n        \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1233</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>parcours</span>\" => []\n      \"<span class=sf-dump-key>cv</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>diplome</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>relevebacc</span>\" => \"<span class=sf-dump-str title=\"82 characters\">livewire-file:mYnYB6Nll1pqMO7F2jHfqzRXX3x0Fv-metaQ291cnMgQ29tcHRhIEZpLnBkZg==-.pdf</span>\"\n      \"<span class=sf-dump-key>releve1</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve2</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve3</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve4</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>terms</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>iteration</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n      \"<span class=sf-dump-key>previewDocument</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcours</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Mention</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>4</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>5</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>6</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>7</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>11</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>8</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">6671a8024aea4507ef919c4db8fdd5470456b6a9bf804d079cfc851eca2e63a8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qm4y</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">nextStep</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373217636\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-715069434 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1260</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImdabXVhVExDVVBMd3M1bmtvQS9KeEE9PSIsInZhbHVlIjoiTmJLTE9rR0JMLzhIb0F2bnFzQUl4OWhTd3dxc3pBbGdMbCthOEZrZDJNUHgxbEFSMk1ieGNDR21KU1c0aDdHWFJBK3U1OHlFOUtTK1BIR0NvdTRCZU9hRmJLYTFpZjZlcE9Xb2RKRVJ5YTk4b0hjU2psT2VaSzlaaHdXSG9YTFYiLCJtYWMiOiIwY2VjMGFkMzY3ZDZlYTZhMmZjNTdhYmVhMTcwMGUxMDVmMWExY2I4MjJlYmE1NzEzMWJjMDYzNjJlMjIwZTRlIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImsxaENFbzlabDErRmdPK211YitKSWc9PSIsInZhbHVlIjoidkUvQXhhaGRGNHFYRkJLZXh3UU14R1hHUnJJK2xsVHZ1WWk0ZXZDS0VQOEhldlQ5bHY4NEpkMFpqakV6bzF0NkR3OTM4M0J2aXNkdlNHTXM4MHovZjI2QXRiM05XOXFXV2YxQlpVOHNpVTVkZnZmUGJNbGxxQmh2aHU4OUpURFIiLCJtYWMiOiJkNDM5Njk2NTAyYzZjNjZiZWQxN2ZiOWQzZmZlNjc2ZGNlZDVmMTg4Zjk4OGUxOWQxMTI5ODZhMzAzN2Y1YjBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715069434\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1080696425 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58120</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"47 characters\">/index.php/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1260</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1260</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImdabXVhVExDVVBMd3M1bmtvQS9KeEE9PSIsInZhbHVlIjoiTmJLTE9rR0JMLzhIb0F2bnFzQUl4OWhTd3dxc3pBbGdMbCthOEZrZDJNUHgxbEFSMk1ieGNDR21KU1c0aDdHWFJBK3U1OHlFOUtTK1BIR0NvdTRCZU9hRmJLYTFpZjZlcE9Xb2RKRVJ5YTk4b0hjU2psT2VaSzlaaHdXSG9YTFYiLCJtYWMiOiIwY2VjMGFkMzY3ZDZlYTZhMmZjNTdhYmVhMTcwMGUxMDVmMWExY2I4MjJlYmE1NzEzMWJjMDYzNjJlMjIwZTRlIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImsxaENFbzlabDErRmdPK211YitKSWc9PSIsInZhbHVlIjoidkUvQXhhaGRGNHFYRkJLZXh3UU14R1hHUnJJK2xsVHZ1WWk0ZXZDS0VQOEhldlQ5bHY4NEpkMFpqakV6bzF0NkR3OTM4M0J2aXNkdlNHTXM4MHovZjI2QXRiM05XOXFXV2YxQlpVOHNpVTVkZnZmUGJNbGxxQmh2aHU4OUpURFIiLCJtYWMiOiJkNDM5Njk2NTAyYzZjNjZiZWQxN2ZiOWQzZmZlNjc2ZGNlZDVmMTg4Zjk4OGUxOWQxMTI5ODZhMzAzN2Y1YjBkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380854.5703</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380854</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080696425\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1499951563 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yVCdhQsriTuZTEQBdHuoxzYc5BgY1Mo8vp1abE4A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499951563\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1092138907 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:40:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRWNGsvSVVFeS9ZTnpLLy9URHBnZnc9PSIsInZhbHVlIjoic2ZXNjFNeXd5Y05rZ3hxV1FQWFVXNk9wTHljRGxtYUpRUmFJdWE1Z1ZpOG00ZTVkcDZpU2pWRzczelZuLzB1RXRaYzRQeVNEakluZkRIM0xCdmxTQVR0MUM3c21ycEJ3WlBXSWMvc2gvSnhKUWc4NWFsaEsreGp0RWxkQ05TUnAiLCJtYWMiOiJjODBhZDMyODE4NmFhZjNjNjEyMWU2Mzc3YTJjYjMyOTQ5MTNiY2M0OTMyZTQ1ZTNhYjY5NDdkYjk0NTY1Mzg5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:40:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IjF6RmgrUzhoaHJwc3Nta2hiOGREV2c9PSIsInZhbHVlIjoibzFXdURodUlSdXdyMjRySnA2ZFhuUjliazZXaDFVaHBOMis4MUp1NGZCcURpZHJaSjVVWVBxNllEeVczaHlWb1FUbTNuemNqM2tPMTlsaW5DRFpLayt3a3gzc0ZRYkxhSndtTC9GSDBtYnRYL1dmQnF0eXBqMjBUbXpSK0JhWjgiLCJtYWMiOiI5YmQ1YzQzNzhmMjhmNDQ2NjlhODJjNzJhYWJhZjU5ODA2YjE2NjMxNDQ0MDFjYzBkY2JkNzQzN2I0NzIyOTMyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:40:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRWNGsvSVVFeS9ZTnpLLy9URHBnZnc9PSIsInZhbHVlIjoic2ZXNjFNeXd5Y05rZ3hxV1FQWFVXNk9wTHljRGxtYUpRUmFJdWE1Z1ZpOG00ZTVkcDZpU2pWRzczelZuLzB1RXRaYzRQeVNEakluZkRIM0xCdmxTQVR0MUM3c21ycEJ3WlBXSWMvc2gvSnhKUWc4NWFsaEsreGp0RWxkQ05TUnAiLCJtYWMiOiJjODBhZDMyODE4NmFhZjNjNjEyMWU2Mzc3YTJjYjMyOTQ5MTNiY2M0OTMyZTQ1ZTNhYjY5NDdkYjk0NTY1Mzg5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:40:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IjF6RmgrUzhoaHJwc3Nta2hiOGREV2c9PSIsInZhbHVlIjoibzFXdURodUlSdXdyMjRySnA2ZFhuUjliazZXaDFVaHBOMis4MUp1NGZCcURpZHJaSjVVWVBxNllEeVczaHlWb1FUbTNuemNqM2tPMTlsaW5DRFpLayt3a3gzc0ZRYkxhSndtTC9GSDBtYnRYL1dmQnF0eXBqMjBUbXpSK0JhWjgiLCJtYWMiOiI5YmQ1YzQzNzhmMjhmNDQ2NjlhODJjNzJhYWJhZjU5ODA2YjE2NjMxNDQ0MDFjYzBkY2JkNzQzN2I0NzIyOTMyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:40:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092138907\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1605989940 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:16</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rama</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Dyer</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1993-02-18</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Ad et veniam exerci</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Facere dicta ratione</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed odio quo sit ea </span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-02-23</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+261325551650</span>\"\n      \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"3 characters\">124</span>\"\n      \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n      \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1233</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"8 characters\">paiement</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605989940\", {\"maxDepth\":0})</script>\n"}}