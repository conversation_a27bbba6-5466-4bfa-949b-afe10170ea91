{"__meta": {"id": "X91044ab607748ad2136b4f85ae8c8a97", "datetime": "2025-07-01 17:38:01", "utime": 1751380681.57482, "method": "GET", "uri": "/caisse/payment/19/5/7/etudiant", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380680.049799, "end": 1751380681.574861, "duration": 1.525062084197998, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1751380680.049799, "relative_start": 0, "end": 1751380680.659603, "relative_end": 1751380680.659603, "duration": 0.6098041534423828, "duration_str": "610ms", "params": [], "collector": null}, {"label": "Application", "start": 1751380680.660485, "relative_start": 0.6106860637664795, "end": 1751380681.574865, "relative_end": 4.0531158447265625e-06, "duration": 0.9143800735473633, "duration_str": "914ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26769384, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php (\\vendor\\livewire\\livewire\\src\\Macros\\livewire-view-extends.blade.php)", "param_count": 4, "params": ["view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php&line=0"}, {"name": "livewire.caf.payment.index (\\resources\\views\\livewire\\caf\\payment\\index.blade.php)", "param_count": 9, "params": ["moyens", "livewireLayout", "errors", "_instance", "currentPage", "newPay", "current_user", "current_niveau", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/caf/payment/index.blade.php&line=0"}, {"name": "livewire.caf.payment.edit (\\resources\\views\\livewire\\caf\\payment\\edit.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "_instance", "moyens", "livewireLayout", "currentPage", "newPay", "current_user", "current_niveau", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/caf/payment/edit.blade.php&line=0"}, {"name": "layouts.backend (\\resources\\views\\layouts\\backend.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/backend.blade.php&line=0"}, {"name": "components.menu (\\resources\\views\\components\\menu.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/components/menu.blade.php&line=0"}, {"name": "components.rightHeader (\\resources\\views\\components\\rightHeader.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/components/rightHeader.blade.php&line=0"}]}, "route": {"uri": "GET caisse/payment/{userId}/{niveauId}/{anneeId}/etudiant", "middleware": "web, auth, auth.caf", "controller": "App\\Http\\Livewire\\EtuPay@__invoke", "as": "caf.caisse.payment.etudiant", "namespace": null, "prefix": "/caisse", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Component.php&line=46\">\\vendor\\livewire\\livewire\\src\\Component.php:46-88</a>"}, "queries": {"nb_statements": 19, "nb_failed_statements": 0, "accumulated_duration": 0.11984999999999998, "accumulated_duration_str": "120ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00453, "duration_str": "4.53ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 3.78}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 3.78, "width_percent": 0.851}, {"sql": "select * from `users` where `users`.`id` = '19' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 24}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:24", "connection": "inscriptionimsaa", "start_percent": 4.631, "width_percent": 0.826}, {"sql": "select * from `niveaux` where `niveaux`.`id` = '5' and `niveaux`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 25}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:25", "connection": "inscriptionimsaa", "start_percent": 5.457, "width_percent": 0.584}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = '7' and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 26}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:26", "connection": "inscriptionimsaa", "start_percent": 6.041, "width_percent": 0.726}, {"sql": "select * from `type_payments` where exists (select * from `niveaux` inner join `niveau_type_payment` on `niveaux`.`id` = `niveau_type_payment`.`niveau_id` where `type_payments`.`id` = `niveau_type_payment`.`type_payment_id` and `niveau_id` = 5 and `niveaux`.`deleted_at` is null)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 57}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 28}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07443999999999999, "duration_str": "74.44ms", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:57", "connection": "inscriptionimsaa", "start_percent": 6.767, "width_percent": 62.111}, {"sql": "select * from `type_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:59", "connection": "inscriptionimsaa", "start_percent": 68.878, "width_percent": 0.551}, {"sql": "select `niveaux`.*, `niveau_type_payment`.`type_payment_id` as `pivot_type_payment_id`, `niveau_type_payment`.`niveau_id` as `pivot_niveau_id`, `niveau_type_payment`.`prix` as `pivot_prix` from `niveaux` inner join `niveau_type_payment` on `niveaux`.`id` = `niveau_type_payment`.`niveau_id` where `niveau_type_payment`.`type_payment_id` = 1 and `niveau_id` = 5 and `niveaux`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 61}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00183, "duration_str": "1.83ms", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:61", "connection": "inscriptionimsaa", "start_percent": 69.428, "width_percent": 1.527}, {"sql": "select `niveaux`.*, `niveau_type_payment`.`type_payment_id` as `pivot_type_payment_id`, `niveau_type_payment`.`niveau_id` as `pivot_niveau_id`, `niveau_type_payment`.`prix` as `pivot_prix` from `niveaux` inner join `niveau_type_payment` on `niveaux`.`id` = `niveau_type_payment`.`niveau_id` where `niveau_type_payment`.`type_payment_id` = 4 and `niveau_id` = 5 and `niveaux`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 61}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0024500000000000004, "duration_str": "2.45ms", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:61", "connection": "inscriptionimsaa", "start_percent": 70.955, "width_percent": 2.044}, {"sql": "select * from `moyen_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\EtuPay.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0088, "duration_str": "8.8ms", "stmt_id": "\\app\\Http\\Livewire\\EtuPay.php:36", "connection": "inscriptionimsaa", "start_percent": 73, "width_percent": 7.343}, {"sql": "select * from `historique_payments` where `user_id` = 19 and `annee_universitaire_id` = 7 and `type_payment_id` = 1 and `historique_payments`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["19", "7", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "25c95dacb9c666d41863bc7eef9da080b3b53c44", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.01654, "duration_str": "16.54ms", "stmt_id": "view::25c95dacb9c666d41863bc7eef9da080b3b53c44:31", "connection": "inscriptionimsaa", "start_percent": 80.342, "width_percent": 13.801}, {"sql": "select * from `historique_payments` where `user_id` = 19 and `annee_universitaire_id` = 7 and `type_payment_id` = 4 and `historique_payments`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["19", "7", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "25c95dacb9c666d41863bc7eef9da080b3b53c44", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "view::25c95dacb9c666d41863bc7eef9da080b3b53c44:31", "connection": "inscriptionimsaa", "start_percent": 94.143, "width_percent": 1.277}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 95.419, "width_percent": 0.659}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 96.078, "width_percent": 0.651}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 96.729, "width_percent": 0.684}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 97.413, "width_percent": 0.701}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 98.114, "width_percent": 0.642}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Models\\User.php:60", "connection": "inscriptionimsaa", "start_percent": 98.757, "width_percent": 0.609}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\helpers.php", "line": 18}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Helpers\\helpers.php:18", "connection": "inscriptionimsaa", "start_percent": 99.366, "width_percent": 0.634}]}, "models": {"data": {"App\\Models\\MoyenPayment": 3, "App\\Models\\TypePayment": 25, "App\\Models\\AnneeUniversitaire": 1, "App\\Models\\Niveau": 3, "App\\Models\\Role": 8, "App\\Models\\User": 2}, "count": 42}, "livewire": {"data": {"etu-pay #vxRjeB8sJUvHGV37lmld": "array:5 [\n  \"data\" => array:5 [\n    \"currentPage\" => \"edit\"\n    \"newPay\" => array:2 [\n      0 => array:6 [\n        \"type_id\" => 1\n        \"type_nom\" => \"Droit d'inscription\"\n        \"max\" => 220000\n        \"code\" => \"\"\n        \"montant\" => \"\"\n        \"moyen\" => \"\"\n      ]\n      1 => array:6 [\n        \"type_id\" => 4\n        \"type_nom\" => \"Certificat de scolarité\"\n        \"max\" => 10000\n        \"code\" => \"\"\n        \"montant\" => \"\"\n        \"moyen\" => \"\"\n      ]\n    ]\n    \"current_user\" => App\\Models\\User {#1771\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:37 [\n        \"id\" => 19\n        \"nom\" => \"ANDRIANARIMANANA\"\n        \"prenom\" => \"Diarintsoa\"\n        \"sexe\" => \"H\"\n        \"date_naissance\" => \"2000-02-20\"\n        \"lieu_naissance\" => \"Toamasina\"\n        \"nationalite\" => \"Malagasy\"\n        \"ville\" => null\n        \"pays\" => null\n        \"adresse\" => \"lot 130 plle21/51 Mangarivotra Sud Toamasina I\"\n        \"telephone1\" => \"0384068869\"\n        \"telephone2\" => \"0340909012\"\n        \"nom_pere\" => null\n        \"nom_mere\" => null\n        \"cin\" => \"301051031102\"\n        \"date_delivrance\" => \"2018-11-26\"\n        \"lieu_delivrance\" => \"Toamasina I\"\n        \"duplicata\" => null\n        \"matricule\" => null\n        \"email\" => \"<EMAIL>\"\n        \"password\" => null\n        \"photo\" => \"media/avatars/avatar0.jpg\"\n        \"inscription_date\" => null\n        \"parcour_id\" => 24\n        \"niveau_id\" => 4\n        \"created_at\" => \"2024-04-26 14:24:18\"\n        \"updated_at\" => \"2025-04-30 13:30:07\"\n        \"deleted_at\" => null\n        \"is_filled\" => 0\n        \"tel_pere\" => null\n        \"tel_mere\" => null\n        \"nom_tuteur\" => null\n        \"tel_tuteur\" => null\n        \"montantenvoi\" => \"220000\"\n        \"reference\" => \"trans id PP240426.1001.A / Trans id PP240426.1037B23\"\n        \"telenvoi\" => \"0321432737\"\n        \"mention_id\" => 9\n      ]\n      #original: array:37 [\n        \"id\" => 19\n        \"nom\" => \"ANDRIANARIMANANA\"\n        \"prenom\" => \"Diarintsoa\"\n        \"sexe\" => \"H\"\n        \"date_naissance\" => \"2000-02-20\"\n        \"lieu_naissance\" => \"Toamasina\"\n        \"nationalite\" => \"Malagasy\"\n        \"ville\" => null\n        \"pays\" => null\n        \"adresse\" => \"lot 130 plle21/51 Mangarivotra Sud Toamasina I\"\n        \"telephone1\" => \"0384068869\"\n        \"telephone2\" => \"0340909012\"\n        \"nom_pere\" => null\n        \"nom_mere\" => null\n        \"cin\" => \"301051031102\"\n        \"date_delivrance\" => \"2018-11-26\"\n        \"lieu_delivrance\" => \"Toamasina I\"\n        \"duplicata\" => null\n        \"matricule\" => null\n        \"email\" => \"<EMAIL>\"\n        \"password\" => null\n        \"photo\" => \"media/avatars/avatar0.jpg\"\n        \"inscription_date\" => null\n        \"parcour_id\" => 24\n        \"niveau_id\" => 4\n        \"created_at\" => \"2024-04-26 14:24:18\"\n        \"updated_at\" => \"2025-04-30 13:30:07\"\n        \"deleted_at\" => null\n        \"is_filled\" => 0\n        \"tel_pere\" => null\n        \"tel_mere\" => null\n        \"nom_tuteur\" => null\n        \"tel_tuteur\" => null\n        \"montantenvoi\" => \"220000\"\n        \"reference\" => \"trans id PP240426.1001.A / Trans id PP240426.1037B23\"\n        \"telenvoi\" => \"0321432737\"\n        \"mention_id\" => 9\n      ]\n      #changes: []\n      #casts: array:3 [\n        \"created_at\" => \"datetime\"\n        \"updated_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: []\n      #guarded: []\n      #rememberTokenName: \"remember_token\"\n      #cascadeDeletes: array:1 [\n        0 => \"notes\"\n      ]\n      #accessToken: null\n      #forceDeleting: false\n      +mediaConversions: []\n      +mediaCollections: []\n      #deletePreservingMedia: false\n      #unAttachedMediaLibraryItems: []\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1779\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 5\n        \"nom\" => \"5ème année\"\n        \"sigle\" => \"M2\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 5\n        \"nom\" => \"5ème année\"\n        \"sigle\" => \"M2\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1788\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 7\n        \"nom\" => \"2025/2026\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 7\n        \"nom\" => \"2025/2026\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n  ]\n  \"name\" => \"etu-pay\"\n  \"view\" => \"livewire.caf.payment.index\"\n  \"component\" => \"App\\Http\\Livewire\\EtuPay\"\n  \"id\" => \"vxRjeB8sJUvHGV37lmld\"\n]"}, "count": 1}, "gate": {"count": 7, "messages": [{"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1161199250 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161199250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380680.807171}, {"message": "[ability => admin, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-347597780 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347597780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380681.510411}, {"message": "[ability => enseignant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1927805413 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927805413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380681.517986}, {"message": "[ability => deraq, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1320308756 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320308756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380681.522736}, {"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1625653774 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625653774\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380681.529118}, {"message": "[ability => secretaire, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-759933631 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759933631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380681.535913}, {"message": "[ability => etudiant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1901644687 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901644687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751380681.540603}]}, "session": {"_token": "fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/payment/19/5/7/etudiant\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751377855\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/caisse/payment/19/5/7/etudiant", "status_code": "<pre class=sf-dump id=sf-dump-1241368850 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1241368850\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-194302659 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-194302659\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1068621642 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1068621642\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-112877066 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/payment</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImN5clNpdFBoTjBMSlF4WkhzNkY3VlE9PSIsInZhbHVlIjoibU5yQ2VNTTZwYTRHWTVHKys0MDZyY1VrVmRmYnRzU1JiWEdBYlZYNUxzY3pnRFhadE1WZXVrWXdKb1hQb1llWDJBeE51eDBjQmZ0bzFWcjc4RmVydGpPZERCM0Vwc0FCcVFCdG9CMkswYSt3OG1hWU5pOHN4ZkUxTHZ3ZWdVMUEiLCJtYWMiOiJiMTBhMDRkMGU4OTc4NDU1MDI4ODc3ZjJkM2Y3YzdiNWM2MzE2OTY0M2EwMWJkYmQxY2JmZWE0MTRkNmFjZGRmIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkNpZGI4cFFMazlLTGlSdFg2RXJGeUE9PSIsInZhbHVlIjoiSG9jVVUvZHYxZisvSVV4dFQ1S2tFb1VSejNxd1pDZmw0bE9HMnBaZlBaRWJTWjRXb1hKbTFBZjZERFVLRVUyenZVV0w4ZG1Nd2Q0NytEcVZaK3J1aDJCVEJuWHZFVHZCZGFGU21RTEx3ZXJrbUVXUy8zSndPbE8zUGZTSXV2VWIiLCJtYWMiOiI2ZmRlYWVkMzczOWI3MjUxOWU4OTNhMGNmYWJkOGMyNzE3N2M0OWY5M2EwNDM1N2UyZDRjOWM2NWNjMThhNGI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-112877066\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-542904087 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57710</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/caisse/payment/19/5/7/etudiant</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/caisse/payment/19/5/7/etudiant</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"41 characters\">/index.php/caisse/payment/19/5/7/etudiant</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/payment</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImN5clNpdFBoTjBMSlF4WkhzNkY3VlE9PSIsInZhbHVlIjoibU5yQ2VNTTZwYTRHWTVHKys0MDZyY1VrVmRmYnRzU1JiWEdBYlZYNUxzY3pnRFhadE1WZXVrWXdKb1hQb1llWDJBeE51eDBjQmZ0bzFWcjc4RmVydGpPZERCM0Vwc0FCcVFCdG9CMkswYSt3OG1hWU5pOHN4ZkUxTHZ3ZWdVMUEiLCJtYWMiOiJiMTBhMDRkMGU4OTc4NDU1MDI4ODc3ZjJkM2Y3YzdiNWM2MzE2OTY0M2EwMWJkYmQxY2JmZWE0MTRkNmFjZGRmIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkNpZGI4cFFMazlLTGlSdFg2RXJGeUE9PSIsInZhbHVlIjoiSG9jVVUvZHYxZisvSVV4dFQ1S2tFb1VSejNxd1pDZmw0bE9HMnBaZlBaRWJTWjRXb1hKbTFBZjZERFVLRVUyenZVV0w4ZG1Nd2Q0NytEcVZaK3J1aDJCVEJuWHZFVHZCZGFGU21RTEx3ZXJrbUVXUy8zSndPbE8zUGZTSXV2VWIiLCJtYWMiOiI2ZmRlYWVkMzczOWI3MjUxOWU4OTNhMGNmYWJkOGMyNzE3N2M0OWY5M2EwNDM1N2UyZDRjOWM2NWNjMThhNGI4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380680.0498</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380680</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542904087\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-156283464 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3fVPC4SnbhmIL9JnY36c5Wf3bMPv5WOAm4KXhpdT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156283464\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1495905930 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:38:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InUyeURoQmV4YlJsVnRUTWQzZXo0MVE9PSIsInZhbHVlIjoieVhkNkNGTHg1QW44S0pFWWEyQTNTUlpaWU5aRHBtaWU1djZSQlA3OFRiRjFNOENmcGpMUFdtZjlaVlpHMkMwZlFhcHV0QzBxSlF5SW1tM1g5V0RPdDZkMThmelJmVVRjNGhka1pESXpzYjlMajNNR3RBNG9EODZ2OUV5QWtyaW8iLCJtYWMiOiI5MjVmNWFiNjQ1NzQ1MjNhYjU3YTc0YzliNjhmMTk0MWM1OTUwNWQxNjE3ODc4ODliZTA5ZWJiNzhjYjZmMzU3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:38:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6Ii9rU3pRdnVhcEl0ZWpYYS9FTGRpL2c9PSIsInZhbHVlIjoiak14RGhOdWVzbzZxOTZrNEdmR0dWc0dtMjcxd0dDaHhrRHAxZTFQaTdrbndPY0ZucFRQZkFNMHhXWXBqMWVFUVVmc2p0ejJFVURhZjZoVzZyeHo2ZnVRL0hZb1dvSE9YeDJBMDlpaGV4dytPdjJlSGNnK2U4TC9rM20zbndtZDMiLCJtYWMiOiJmNGZhMmExYmUzY2ZkMGVkYTJjYjMyOTYzOWYxZDE0YTQ2ODM5MWI3OWIyMDQ5ZTQ3NWJiN2EyYzIyNjg1ZjdhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:38:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InUyeURoQmV4YlJsVnRUTWQzZXo0MVE9PSIsInZhbHVlIjoieVhkNkNGTHg1QW44S0pFWWEyQTNTUlpaWU5aRHBtaWU1djZSQlA3OFRiRjFNOENmcGpMUFdtZjlaVlpHMkMwZlFhcHV0QzBxSlF5SW1tM1g5V0RPdDZkMThmelJmVVRjNGhka1pESXpzYjlMajNNR3RBNG9EODZ2OUV5QWtyaW8iLCJtYWMiOiI5MjVmNWFiNjQ1NzQ1MjNhYjU3YTc0YzliNjhmMTk0MWM1OTUwNWQxNjE3ODc4ODliZTA5ZWJiNzhjYjZmMzU3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:38:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6Ii9rU3pRdnVhcEl0ZWpYYS9FTGRpL2c9PSIsInZhbHVlIjoiak14RGhOdWVzbzZxOTZrNEdmR0dWc0dtMjcxd0dDaHhrRHAxZTFQaTdrbndPY0ZucFRQZkFNMHhXWXBqMWVFUVVmc2p0ejJFVURhZjZoVzZyeHo2ZnVRL0hZb1dvSE9YeDJBMDlpaGV4dytPdjJlSGNnK2U4TC9rM20zbndtZDMiLCJtYWMiOiJmNGZhMmExYmUzY2ZkMGVkYTJjYjMyOTYzOWYxZDE0YTQ2ODM5MWI3OWIyMDQ5ZTQ3NWJiN2EyYzIyNjg1ZjdhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:38:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495905930\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1685477086 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/caisse/payment/19/5/7/etudiant</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751377855</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685477086\", {\"maxDepth\":0})</script>\n"}}