{"__meta": {"id": "X8bfcc0fa20ec9bf4cced486fc37a2da7", "datetime": "2025-07-01 17:37:46", "utime": 1751380666.069735, "method": "POST", "uri": "/livewire/message/utilisateurs", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380665.356286, "end": 1751380666.069765, "duration": 0.7134790420532227, "duration_str": "713ms", "measures": [{"label": "Booting", "start": 1751380665.356286, "relative_start": 0, "end": 1751380665.771126, "relative_end": 1751380665.771126, "duration": 0.4148399829864502, "duration_str": "415ms", "params": [], "collector": null}, {"label": "Application", "start": 1751380665.772276, "relative_start": 0.41598987579345703, "end": 1751380666.069768, "relative_end": 2.86102294921875e-06, "duration": 0.29749202728271484, "duration_str": "297ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27432376, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.utilisateurs.index (\\resources\\views\\livewire\\utilisateurs\\index.blade.php)", "param_count": 33, "params": ["users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "errors", "_instance", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "showInscriptionModal", "selectedUserForInscription", "newInscription", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/index.blade.php&line=0"}, {"name": "livewire.utilisateurs.liste (\\resources\\views\\livewire\\utilisateurs\\liste.blade.php)", "param_count": 35, "params": ["__env", "app", "errors", "_instance", "users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "showInscriptionModal", "selectedUserForInscription", "newInscription", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/liste.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01191, "accumulated_duration_str": "11.91ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0040999999999999995, "duration_str": "4.1ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 34.425}, {"sql": "select * from `media` where `media`.`id` in (23, 24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "inscriptionimsaa", "start_percent": 34.425, "width_percent": 6.633}, {"sql": "select count(*) as aggregate from `users` where (`nom` like '%diarin%' or `prenom` like '%diarin%' or `email` like '%diarin%' or `matricule` like '%diarin%') and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `roles`.`id` = '5') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%diarin%", "%diarin%", "%diarin%", "%diarin%", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0034500000000000004, "duration_str": "3.45ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 41.058, "width_percent": 28.967}, {"sql": "select `id`, `nom`, `prenom`, `email`, `telephone1`, `photo`, `matricule`, `created_at`, `updated_at` from `users` where (`nom` like '%diarin%' or `prenom` like '%diarin%' or `email` like '%diarin%' or `matricule` like '%diarin%') and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `roles`.`id` = '5') and `users`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["%diarin%", "%diarin%", "%diarin%", "%diarin%", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00278, "duration_str": "2.78ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 70.025, "width_percent": 23.342}, {"sql": "select `roles`.`id`, `roles`.`nom`, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (19, 51)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 93.367, "width_percent": 6.633}]}, "models": {"data": {"App\\Models\\Role": 2, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": 2, "App\\Models\\User": 3}, "count": 7}, "livewire": {"data": {"utilisateurs #Cz9Fjq7A30Otr0VSTf6u": "array:5 [\n  \"data\" => array:20 [\n    \"currentPage\" => \"liste\"\n    \"newUser\" => []\n    \"editUser\" => []\n    \"mediaItems\" => <PERSON><PERSON>\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#1910\n      #items: array:2 [\n        0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#1580\n          #connection: \"mysql\"\n          #table: \"media\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:18 [\n            \"id\" => 23\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"d8931ff8-fdaa-4963-a160-f10d21c1ba16\"\n            \"collection_name\" => \"CV\"\n            \"name\" => \"releve RAZAFISOA (1)\"\n            \"file_name\" => \"releve-RAZAFISOA-(1).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #original: array:18 [\n            \"id\" => 23\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"d8931ff8-fdaa-4963-a160-f10d21c1ba16\"\n            \"collection_name\" => \"CV\"\n            \"name\" => \"releve RAZAFISOA (1)\"\n            \"file_name\" => \"releve-RAZAFISOA-(1).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"manipulations\" => \"array\"\n            \"custom_properties\" => \"array\"\n            \"generated_conversions\" => \"array\"\n            \"responsive_images\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: array:2 [\n            0 => \"original_url\"\n            1 => \"preview_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: []\n        }\n        1 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#1744\n          #connection: \"mysql\"\n          #table: \"media\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:18 [\n            \"id\" => 24\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"7ce3e5d9-5cd3-42fa-9289-62243e8f3cc2\"\n            \"collection_name\" => \"Diplome\"\n            \"name\" => \"releve RAZAFISOA (2)\"\n            \"file_name\" => \"releve-RAZAFISOA-(2).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 2\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #original: array:18 [\n            \"id\" => 24\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"7ce3e5d9-5cd3-42fa-9289-62243e8f3cc2\"\n            \"collection_name\" => \"Diplome\"\n            \"name\" => \"releve RAZAFISOA (2)\"\n            \"file_name\" => \"releve-RAZAFISOA-(2).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 2\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"manipulations\" => \"array\"\n            \"custom_properties\" => \"array\"\n            \"generated_conversions\" => \"array\"\n            \"responsive_images\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: array:2 [\n            0 => \"original_url\"\n            1 => \"preview_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: []\n        }\n      ]\n      #escapeWhenCastingToString: false\n      +collectionName: null\n      +formFieldName: null\n    }\n    \"isEtu\" => false\n    \"query\" => \"diarin\"\n    \"newPasswd\" => null\n    \"filterRole\" => \"5\"\n    \"filterStatus\" => \"\"\n    \"perPage\" => 10\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"selectedUsers\" => []\n    \"selectAll\" => false\n    \"rolePermissions\" => array:2 [\n      \"roles\" => array:7 [\n        0 => array:3 [\n          \"role_id\" => 1\n          \"role_nom\" => \"superadmin\"\n          \"active\" => true\n        ]\n        1 => array:3 [\n          \"role_id\" => 2\n          \"role_nom\" => \"enseignant\"\n          \"active\" => false\n        ]\n        2 => array:3 [\n          \"role_id\" => 3\n          \"role_nom\" => \"deraq\"\n          \"active\" => false\n        ]\n        3 => array:3 [\n          \"role_id\" => 4\n          \"role_nom\" => \"secretaire\"\n          \"active\" => false\n        ]\n        4 => array:3 [\n          \"role_id\" => 5\n          \"role_nom\" => \"etudiant\"\n          \"active\" => false\n        ]\n        5 => array:3 [\n          \"role_id\" => 6\n          \"role_nom\" => \"admin\"\n          \"active\" => false\n        ]\n        6 => array:3 [\n          \"role_id\" => 7\n          \"role_nom\" => \"caf\"\n          \"active\" => false\n        ]\n      ]\n      \"permissions\" => []\n    ]\n    \"showInscriptionModal\" => true\n    \"selectedUserForInscription\" => array:5 [\n      \"id\" => 19\n      \"nom\" => \"ANDRIANARIMANANA\"\n      \"prenom\" => \"Diarintsoa\"\n      \"email\" => \"<EMAIL>\"\n      \"current_inscriptions\" => array:1 [\n        0 => array:5 [\n          \"id\" => 923\n          \"parcour\" => \"ADMINISTRATION DES ETABLISSEMENTS DE SANTE\"\n          \"niveau\" => \"4ème année\"\n          \"annee\" => \"2024/2025\"\n          \"created_at\" => \"30/04/2025\"\n        ]\n      ]\n    ]\n    \"newInscription\" => array:3 [\n      \"annee_universitaire_id\" => \"7\"\n      \"niveau_id\" => \"5\"\n      \"parcour_id\" => \"24\"\n    ]\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"utilisateurs\"\n  \"view\" => \"livewire.utilisateurs.index\"\n  \"component\" => \"App\\Http\\Livewire\\Utilisateurs\"\n  \"id\" => \"Cz9Fjq7A30Otr0VSTf6u\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/payment\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751377855\n]"}, "request": {"path_info": "/livewire/message/utilisateurs", "status_code": "<pre class=sf-dump id=sf-dump-203923479 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-203923479\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-493291109 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-493291109\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-994135038 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cz9Fjq7A30Otr0VSTf6u</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">utilisateurs</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"26 characters\">habilitations/utilisateurs</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">21755fe9</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:20</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>mediaItems</span>\" => []\n      \"<span class=sf-dump-key>isEtu</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"6 characters\">diarin</span>\"\n      \"<span class=sf-dump-key>newPasswd</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filterRole</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>filterStatus</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>selectedUsers</span>\" => []\n      \"<span class=sf-dump-key>selectAll</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>rolePermissions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>roles</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>4</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>6</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>permissions</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>showInscriptionModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>selectedUserForInscription</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">ANDRIANARIMANANA</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Diarintsoa</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>current_inscriptions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>923</span>\n            \"<span class=sf-dump-key>parcour</span>\" => \"<span class=sf-dump-str title=\"42 characters\">ADMINISTRATION DES ETABLISSEMENTS DE SANTE</span>\"\n            \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str title=\"10 characters\">4&#232;me ann&#233;e</span>\"\n            \"<span class=sf-dump-key>annee</span>\" => \"<span class=sf-dump-str title=\"9 characters\">2024/2025</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">30/04/2025</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>newInscription</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>annee_universitaire_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n        \"<span class=sf-dump-key>niveau_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n        \"<span class=sf-dump-key>parcour_id</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mediaItems</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Spatie\\MediaLibrary\\MediaCollections\\Models\\Media</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>23</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>24</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => \"<span class=sf-dump-str title=\"71 characters\">Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">9b79062d5c1bdd4c4db44b2371a4176b7facae770e52832da45dcd22d371b051</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">ccci</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">newInscription.parcour_id</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994135038\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2110597194 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1722</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"74 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?filterRole=5&amp;query=diarin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlliTlFzTzJhZnVXaFJLVFpCWUdGTGc9PSIsInZhbHVlIjoid2hnL1o1NmpsL29BVy8rRTdhc1VlbHBWb1pDM0dwSUJDUWFGdU15OE5nVHlVcE5iMXJqUm1WQlE4K1NRT3pPcDdGYSs1KzVaN2ZvQyt6SmluL1Q0Y3NMRVZ0Z25Dd1cxSm1DSEpYb3NwZ2JENEJneTBWR1hjc1RrNThDSGRiRjYiLCJtYWMiOiI2MmZkNWE5MTM3ZjYxNTQ0NjdhOTk1Y2IyYThmZjE5YWFmZjk5ZjBhMjg5YjA5OTIzOWRiMDFmOTZiOTIwZTJjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6Ikk4dkhjdDkxM2NKQkpiZ2M0em1IU1E9PSIsInZhbHVlIjoiTS9LYmNrOVEzUlJVd3NScTQ2YXFwUHRENEZJUW9zQWExWjJUSURsUHMzMWN2RHNNNXJxeVZPWnA0YXdoQjFNUWppeUlWa0F5S1hNc0hwK2tmN1ByaEVicytsdlF4cG1ucFlUV3J3T0FEdmV3cmU4bE9xNEs4TmJRWmNQRFdsaHYiLCJtYWMiOiJhOTEzY2ZlYmQwMzc0ZWJjYmFlNzgyZmMyZTQ1YjYxNTI3MGFjMjllZWViZWVlZWQ4MjYyOTdmN2Q4NDg3Nzc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110597194\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-991946387 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57673</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/index.php/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1722</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1722</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"74 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?filterRole=5&amp;query=diarin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlliTlFzTzJhZnVXaFJLVFpCWUdGTGc9PSIsInZhbHVlIjoid2hnL1o1NmpsL29BVy8rRTdhc1VlbHBWb1pDM0dwSUJDUWFGdU15OE5nVHlVcE5iMXJqUm1WQlE4K1NRT3pPcDdGYSs1KzVaN2ZvQyt6SmluL1Q0Y3NMRVZ0Z25Dd1cxSm1DSEpYb3NwZ2JENEJneTBWR1hjc1RrNThDSGRiRjYiLCJtYWMiOiI2MmZkNWE5MTM3ZjYxNTQ0NjdhOTk1Y2IyYThmZjE5YWFmZjk5ZjBhMjg5YjA5OTIzOWRiMDFmOTZiOTIwZTJjIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6Ikk4dkhjdDkxM2NKQkpiZ2M0em1IU1E9PSIsInZhbHVlIjoiTS9LYmNrOVEzUlJVd3NScTQ2YXFwUHRENEZJUW9zQWExWjJUSURsUHMzMWN2RHNNNXJxeVZPWnA0YXdoQjFNUWppeUlWa0F5S1hNc0hwK2tmN1ByaEVicytsdlF4cG1ucFlUV3J3T0FEdmV3cmU4bE9xNEs4TmJRWmNQRFdsaHYiLCJtYWMiOiJhOTEzY2ZlYmQwMzc0ZWJjYmFlNzgyZmMyZTQ1YjYxNTI3MGFjMjllZWViZWVlZWQ4MjYyOTdmN2Q4NDg3Nzc3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380665.3563</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380665</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991946387\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1431888266 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3fVPC4SnbhmIL9JnY36c5Wf3bMPv5WOAm4KXhpdT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431888266\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:37:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjlWWVV4T1pnUU9WUUlTNG4xekYwRlE9PSIsInZhbHVlIjoib21SVDdUeDBjbkJKRThhQjBFSU51bkx2ekVYeGYxc1pzMUJHdjFES3YyUWo0TXFPZ2pMS0VPOEYwSE40YTlqaTFVTjduSElKb25HblBpNGk5aG52MU9uV3d4bWNWbWxQb0FXR3hZcVduZmErOUJoeXhmSjBJOG5Fb1lJYlNjQUIiLCJtYWMiOiI1YWNiMGJlM2QyZDIyNDA4ODZhODYwODg5ZTMwNWEyYTQ3ZTkzOTY3NWE3ODM5YWY5NmI1ZDQ4NzViYjVjY2E1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:37:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IlBnb1o1R2NsSFJ5dzRjWEs4QVZmTVE9PSIsInZhbHVlIjoiOEo3eHAxemdwcktDWXpXUG9JdVlSbTBabWZlUlhqMkNwSnA4UTV5UnlYbnk0TWM1VVdHVU8yaXlXQ20xN3diUll3TGQvMWpqbVRORHErTzhoU29MZkRBVDZvUXIxbHdDMWtXNURjUnZKdXhrUHFWM0o1UVlhTHZEeWRMbjVSdjciLCJtYWMiOiI2ZDBkMGUyOTg3YmYyNTE4NzMxNDliYmZiMzJkMzFlODZkOWJkNGUzMTQ4ZWJhNjFhNTMxZWE4N2I0NzViNjRhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:37:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjlWWVV4T1pnUU9WUUlTNG4xekYwRlE9PSIsInZhbHVlIjoib21SVDdUeDBjbkJKRThhQjBFSU51bkx2ekVYeGYxc1pzMUJHdjFES3YyUWo0TXFPZ2pMS0VPOEYwSE40YTlqaTFVTjduSElKb25HblBpNGk5aG52MU9uV3d4bWNWbWxQb0FXR3hZcVduZmErOUJoeXhmSjBJOG5Fb1lJYlNjQUIiLCJtYWMiOiI1YWNiMGJlM2QyZDIyNDA4ODZhODYwODg5ZTMwNWEyYTQ3ZTkzOTY3NWE3ODM5YWY5NmI1ZDQ4NzViYjVjY2E1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:37:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IlBnb1o1R2NsSFJ5dzRjWEs4QVZmTVE9PSIsInZhbHVlIjoiOEo3eHAxemdwcktDWXpXUG9JdVlSbTBabWZlUlhqMkNwSnA4UTV5UnlYbnk0TWM1VVdHVU8yaXlXQ20xN3diUll3TGQvMWpqbVRORHErTzhoU29MZkRBVDZvUXIxbHdDMWtXNURjUnZKdXhrUHFWM0o1UVlhTHZEeWRMbjVSdjciLCJtYWMiOiI2ZDBkMGUyOTg3YmYyNTE4NzMxNDliYmZiMzJkMzFlODZkOWJkNGUzMTQ4ZWJhNjFhNTMxZWE4N2I0NzViNjRhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:37:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-381194680 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/payment</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751377855</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381194680\", {\"maxDepth\":0})</script>\n"}}