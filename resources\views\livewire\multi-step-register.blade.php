<form method="POST" wire:submit.prevent="register()" enctype="multipart/form-data">
    <div class="block block-rounded">
        <div class="block-content">
            <!-- Progress Bar -->
            <div class="progress-steps mb-4">
                <div class="progress" style="height: 4px;">
                    <div class="progress-bar" role="progressbar" 
                         style="width: {{ $this->getProgressPercentage() }}%;" 
                         aria-valuenow="{{ $this->getProgressPercentage() }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>
                <div class="d-flex justify-content-between mt-2">
                    <div class="step {{ $this->isStepComplete('parcours') ? 'completed' : '' }}" 
                         wire:click="goToStep('parcours')" 
                         style="cursor: pointer;">
                        <div class="step-icon">
                            <i class="fa fa-graduation-cap"></i>
                        </div>
                        <div class="step-label">Parcours</div>
                    </div>
                    <div class="step {{ $this->isStepComplete('informations') ? 'completed' : '' }}"
                         wire:click="goToStep('informations')"
                         style="cursor: pointer;">
                        <div class="step-icon">
                            <i class="fa fa-user"></i>
                        </div>
                        <div class="step-label">Informations</div>
                    </div>
                    <div class="step {{ $this->isStepComplete('documents') ? 'completed' : '' }}"
                         wire:click="goToStep('documents')"
                         style="cursor: pointer;">
                        <div class="step-icon">
                            <i class="fa fa-file"></i>
                        </div>
                        <div class="step-label">Documents</div>
                    </div>
                    <div class="step {{ $this->isStepComplete('paiement') ? 'completed' : '' }}"
                         wire:click="goToStep('paiement')"
                         style="cursor: pointer;">
                        <div class="step-icon">
                            <i class="fa fa-credit-card"></i>
                        </div>
                        <div class="step-label">Paiement</div>
                    </div>
                </div>
            </div>

            <!-- Navigation buttons -->
            <div class="d-flex justify-content-between mt-4 mb-4">
                <button type="button" class="btn btn-alt-secondary" wire:click="previousStep" {{ $currentStep === 'parcours' ? 'disabled' : '' }} wire:loading.attr="disabled">
                    <i class="fa fa-arrow-left me-1"></i> 
                    <span wire:loading.remove wire:target="previousStep">Précédent</span>
                    <span wire:loading wire:target="previousStep">
                        <i class="fa fa-spinner fa-spin me-1"></i> Chargement...
                    </span>
                </button>
                @if($currentStep === 'paiement')
                    <button type="button" class="btn btn-primary" wire:click="register" wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="register">
                            <i class="fa fa-fw fa-check me-1"></i> S'inscrire
                        </span>
                        <span wire:loading wire:target="register">
                            <i class="fa fa-spinner fa-spin me-1"></i> Inscription en cours...
                        </span>
                    </button>
                @else
                    <button type="button" class="btn btn-primary" wire:click="nextStep" {{ !$this->canProceedToNextStep() ? 'disabled' : '' }} wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="nextStep">Suivant <i class="fa fa-arrow-right ms-1"></i></span>
                        <span wire:loading wire:target="nextStep">
                            <i class="fa fa-spinner fa-spin me-1"></i> Chargement...
                        </span>
                    </button>
                @endif
            </div>

            @error('step')
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-circle me-1"></i>
                    {{ $message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @enderror

            <!-- Form Content -->
            <div class="form-content">
                @if($currentStep === 'parcours')
                    <h2 class="content-heading text-center border-bottom my-4 pb-2">Choix Parcours</h2>
    <div class="row g-4 mb-4">
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-select">Niveau<span class="text-danger">*</span></label>
            <select class="form-select form-control-alt @error('niveau') is-invalid @enderror" wire:model="niveau"
                id="example-select" name="example-select">
                <option selected value="">-- Choisir Niveau --</option>
                <option value="1">1ère année</option>
                <option value="2">2ème année (DTS)</option>
                <option value="3">3ème année (Licence)</option>
                <option value="4">4ème année (Master I)</option>
                <option value="5">5ème année (Master II)</option>
            </select>

            @error('niveau')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-select1">Mention<span class="text-danger">*</span></label>
            <select class="form-select form-control-alt @error('parcour') is-invalid @enderror" wire:model="parcour"
                id="example-select1" name="example-select1">
                @if ($parcours->count() == 0)
                    <option selected value="">-- Veuillez d'abord choisir le niveau --</option>
                @endif

                @foreach ($parcours as $parcour)
                    <option value="{{ $parcour->id }}">{{ $parcour->nom }}</option>
                @endforeach
            </select>

            @error('parcour')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
    </div>
                @elseif($currentStep === 'informations')
    <h2 class="content-heading text-center border-bottom my-4 pb-2">Renseignement sur l'étudiant</h2>
    <div class="row g-4">
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-nom">
                <i class="fa fa-user me-1"></i>Nom <span class="text-danger">*</span>
                <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Veuillez entrer votre nom complet"></i>
            </label>
            <input type="text" wire:model="newUser.nom"
                class="form-control form-control-alt @error('newUser.nom') is-invalid @enderror" id="input-nom"
                name="firstname" placeholder="Nom de l'étudiant">
            @error('newUser.nom')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-prenom">
                <i class="fa fa-user me-1"></i>Prénom <span class="text-danger">*</span>
                <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Veuillez entrer votre prénom"></i>
            </label>
            <input type="text" wire:model="newUser.prenom"
                class="form-control form-control-alt @error('newUser.prenom') is-invalid @enderror"
                id="input-prenom" name="lastname" placeholder="Prénom de l'étudiant">
            @error('newUser.prenom')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-date-naissance">Date de naissance <span
                    class="text-danger">*</span></label>
            <input type="date" wire:model="newUser.date_naissance" 
                class="form-control form-control-alt @error('newUser.date_naissance') is-invalid @enderror"
                id="input-date-naissance" name="date_naissance" data-week-start="1" data-autoclose="true"
                data-today-highlight="true" data-date-format="dd/mm/yyyy" placeholder="jj/mm/aaaa">
            @error('newUser.date_naissance')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-sexe">Sexe <span class="text-danger">*</span></label>
            <select class="form-select form-control-alt @error('newUser.sexe') is-invalid @enderror"
                wire:model="newUser.sexe" id="input-sexe" name="example-select">
                <option selected value="">-- Choisir sexe --</option>
                <option value="H">Homme</option>
                <option value="F">Femme</option>
            </select>
            @error('newUser.sexe')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-lieu-naissance">Lieu de naissance <span
                    class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.lieu_naissance"
                class="form-control form-control-alt @error('newUser.lieu_naissance') is-invalid @enderror"
                id="input-lieu-naissance" name="example-text-input" placeholder="Lieu de naissance">
            @error('newUser.lieu_naissance')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-adresse">Adresse <span class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.adresse"
                class="form-control form-control-alt @error('newUser.adresse') is-invalid @enderror"
                id="input-adresse" name="address" placeholder="Adresse actuel">
            @error('newUser.adresse')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-nationalite">Nationalité <span class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.nationalite"
                class="form-control form-control-alt @error('newUser.nationalite') is-invalid @enderror"
                id="input-nationalite" name="example-text-input" placeholder="Nationalité">
            @error('newUser.nationalite')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        {{-- <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Ville <span class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.ville"
                class="form-control form-control-alt @error('newUser.ville') is-invalid @enderror"
                id="example-text-input" name="example-text-input" placeholder="Text Input">

            @error('newUser.ville')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Pays <span class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.pays"
                class="form-control form-control-alt @error('newUser.pays') is-invalid @enderror"
                id="example-text-input" name="example-text-input" placeholder="Text Input">

            @error('newUser.pays')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div> --}}

        <div class="col-md-12 col-lg-6">
                            <label class="form-label" for="example-email-input">
                                <i class="fa fa-envelope me-1"></i>Email <span class="text-danger">*</span>
                                <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Veuillez entrer une adresse email valide"></i>
                            </label>
                            <div class="input-group">
                                <input type="email" wire:model.debounce.500ms="newUser.email"
                class="form-control form-control-alt @error('newUser.email') is-invalid @enderror"
                                    id="example-email-input" name="example-email-input" placeholder="<EMAIL>">
                                <button class="btn btn-alt-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-chevron-down"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" style="width: 100%;">
                                    <li><a class="dropdown-item" href="#" wire:click.prevent="$set('newUser.email', '{{ $newUser['nom'] ?? '' }}.{{ $newUser['prenom'] ?? '' }}@gmail.com')">@gmail.com</a></li>
                                    <li><a class="dropdown-item" href="#" wire:click.prevent="$set('newUser.email', '{{ $newUser['nom'] ?? '' }}.{{ $newUser['prenom'] ?? '' }}@yahoo.com')">@yahoo.com</a></li>
                                    <li><a class="dropdown-item" href="#" wire:click.prevent="$set('newUser.email', '{{ $newUser['nom'] ?? '' }}.{{ $newUser['prenom'] ?? '' }}@hotmail.com')">@hotmail.com</a></li>
                                </ul>
                            </div>
                            <div class="form-text" wire:loading wire:target="newUser.email">
                                <i class="fa fa-spinner fa-spin"></i> Vérification de l'email...
                            </div>
            @error('newUser.email')
                                <div class="invalid-feedback d-block">
                                    <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                </div>
            @enderror
        </div>

        <div class="col-md-12 col-lg-6">
                            <label class="form-label" for="example-text-input">
                                <i class="fa fa-phone me-1"></i>Telephone <span class="text-danger">*</span>
                                <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Format: +261 XX XX XXX XX"></i>
                            </label>
                            <input type="text" wire:model.debounce.500ms="newUser.telephone1"
                class="form-control form-control-alt @error('newUser.telephone1') is-invalid @enderror"
                                id="example-text-input" name="example-text-input" placeholder="+261 XX XX XXX XX">
            @error('newUser.telephone1')
                                <div class="invalid-feedback d-block">
                                    <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                </div>
            @enderror
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Telephone (2)</label>
            <input type="text" wire:model="newUser.telephone2" class="form-control form-control-alt"
                id="example-text-input" name="example-text-input" placeholder="Numéro téléphone (Facultatif)">
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">CIN</label>
            <input type="text" wire:model="newUser.cin"
                class="form-control form-control-alt @error('newUser.cin') is-invalid @enderror"
                id="example-text-input" name="example-text-input"
                placeholder="Numéro du CIN (Laissez vide si vous êtes mineur)">

            @error('newUser.cin')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Date de délivrance </label>
            <input type="date" wire:model="newUser.date_delivrance"
                class="form-control form-control-alt @error('newUser.date_delivrance') is-invalid @enderror"
                id="date_delivrance" name="date_delivrance" data-week-start="1" data-autoclose="true"
                data-today-highlight="true" data-date-format="dd/mm/yyyy" placeholder="jj/mm/aaaa">

            @error('newUser.date_delivrance')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Lieu de délivrance</label>
            <input type="text" wire:model="newUser.lieu_delivrance"
                class="form-control form-control-alt @error('newUser.lieu_delivrance') is-invalid @enderror"
                id="example-text-input" name="example-text-input" placeholder="Lieu de délivrance CIN">

            @error('newUser.lieu_delivrance')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
    </div>
                @elseif($currentStep === 'documents')
    <h2 class="content-heading text-center border-bottom my-4 pb-2">Pièces jointes à fournir</h2>
    @if ($niveau == 1)
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Relevé ou Diplôme BACC
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="relevebacc"
                                    class="form-control @error('relevebacc') is-invalid @enderror" 
                                    id="upload1{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="relevebacc" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($relevebacc)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($relevebacc->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $relevebacc->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $relevebacc->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('relevebacc')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('relevebacc')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>
        </div>
    @elseif ($niveau == 2)
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control @error('cv') is-invalid @enderror"
                                    id="upload2{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                @if ($cv)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $cv->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $cv->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('cv')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme BACC
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control @error('diplome') is-invalid @enderror"
                                    id="upload3{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($diplome)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $diplome->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $diplome->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('diplome')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Relevé L1
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve1"
                                    class="form-control @error('releve1') is-invalid @enderror"
                                    id="upload4{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve1" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($releve1)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($releve1->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $releve1->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $releve1->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve1')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('releve1')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>
        </div>
    @elseif ($niveau == 3)
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control @error('cv') is-invalid @enderror"
                                    id="upload5{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                @if ($cv)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $cv->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $cv->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('cv')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme BACC
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control @error('diplome') is-invalid @enderror"
                                    id="upload6{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($diplome)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $diplome->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $diplome->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('diplome')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Relevé L2
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve2"
                                    class="form-control @error('releve2') is-invalid @enderror"
                                    id="upload7{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve2" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($releve2)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($releve2->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $releve2->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $releve2->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve2')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('releve2')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>
        </div>
    @elseif ($niveau == 4)
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control @error('cv') is-invalid @enderror"
                                    id="upload8{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                @if ($cv)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $cv->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $cv->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('cv')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme de DTS ou Licence
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control @error('diplome') is-invalid @enderror"
                                    id="upload9{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($diplome)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $diplome->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $diplome->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('diplome')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-8">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Attestation de travail (2 ans d'expérience)
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve3"
                                    class="form-control @error('releve3') is-invalid @enderror"
                                    id="upload10{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve3" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($releve3)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($releve3->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $releve3->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $releve3->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve3')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('releve3')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>
        </div>
    @elseif ($niveau == 5)
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control @error('cv') is-invalid @enderror"
                                    id="upload11{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                @if ($cv)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $cv->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $cv->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('cv')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme de Licence
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control @error('diplome') is-invalid @enderror"
                                    id="upload12{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($diplome)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $diplome->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $diplome->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('diplome')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Relevé M1
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve4"
                                    class="form-control @error('releve4') is-invalid @enderror"
                                    id="upload13{{ $iteration }}"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve4" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                @if ($releve4)
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            @if (in_array($releve4->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                                                <div class="me-2">
                                                    <img src="{{ $releve4->temporaryUrl() }}" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            @else
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            @endif
                                            <span class="text-truncate">{{ $releve4->getClientOriginalName() }}</span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve4')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                @endif

                @error('releve4')
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i>{{ $message }}
                                    </div>
                @enderror
            </div>
        </div>
    @else
        <div class="alert alert-warning d-flex align-items-center justify-content-between" role="alert">
            <div class="flex-grow-1 me-3">
                <p class="mb-0">
                    Choisissez d'abord votre niveau
                </p>
            </div>
            <div class="flex-shrink-0">
                <i class="fa fa-fw fa-exclamation-circle"></i>
            </div>
        </div>
    @endif
                @elseif($currentStep === 'paiement')
                    <h2 class="content-heading text-center border-bottom my-4 pb-2">Suivi de paiement</h2>
    <div class="alert alert-info d-flex align-items-center" role="alert">
        <div class="flex-shrink-0">
            <i class="fa fa-fw fa-info-circle"></i>
        </div>
        <div class="flex-grow-1 ms-3">
            <p class="mb-0">
                                Le paiement du droit d'inscription et les écolages se fait via Mobile Banking sur le
                                numéro <strong>+261 32 78 337 36</strong>, au Nom de Monsieur Elia Gideon (Directeur Général de l'IMSAA).
            </p>
        </div>
    </div>
    <div class="row g-4">
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Référence de paiement <span
                    class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.reference"
                class="form-control form-control-alt @error('newUser.reference') is-invalid @enderror"
                id="example-text-input" name="firstname" placeholder="Référence de paiement">

            @error('newUser.reference')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">N° Téléphone de l'envoyeur <span
                    class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.telenvoi"
                class="form-control form-control-alt @error('newUser.telenvoi') is-invalid @enderror"
                id="example-text-input" name="lastname" placeholder="N° Téléphone de l'envoyeur">

            @error('newUser.telenvoi')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Montant envoyé<span
                    class="text-danger">*</span></label>
            <input type="number" wire:model="newUser.montantenvoi"
                class="form-control form-control-alt @error('newUser.montantenvoi') is-invalid @enderror"
                id="example-text-input" name="lastname" placeholder="Montant en ariary">

            @error('newUser.montantenvoi')
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
                    </div>
                @endif
    </div>

            <!-- Bottom Navigation -->
            <div class="d-flex justify-content-between mt-4 mb-4">
                <button type="button" class="btn btn-alt-secondary" wire:click="previousStep" {{ $currentStep === 'parcours' ? 'disabled' : '' }} wire:loading.attr="disabled">
                    <i class="fa fa-arrow-left me-1"></i>
                    <span wire:loading.remove wire:target="previousStep">Précédent</span>
                    <span wire:loading wire:target="previousStep">
                        <i class="fa fa-spinner fa-spin me-1"></i> Chargement...
                    </span>
                </button>
                @if($currentStep === 'paiement')
                    <button type="button" class="btn btn-primary" wire:click="register" wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="register">
                            <i class="fa fa-fw fa-check me-1"></i> S'inscrire
                        </span>
                        <span wire:loading wire:target="register">
                            <i class="fa fa-spinner fa-spin me-1"></i> Inscription en cours...
                        </span>
                    </button>
                @else
                    <button type="button" class="btn btn-primary" wire:click="nextStep" {{ !$this->canProceedToNextStep() ? 'disabled' : '' }} wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="nextStep">Suivant <i class="fa fa-arrow-right ms-1"></i></span>
                        <span wire:loading wire:target="nextStep">
                            <i class="fa fa-spinner fa-spin me-1"></i> Chargement...
                        </span>
                    </button>
                @endif
            </div>

            <!-- Terms and conditions -->
            @if($currentStep === 'paiement')
                <div class="mt-4">
        <div class="form-check mb-3">
            <input class="form-check-input @error('terms') is-invalid @enderror" wire:model="terms" type="checkbox"
                            value="" id="signup-terms" wire:loading.attr="disabled">
            <label class="form-check-label" for="signup-terms">Je déclare avoir pris connaissance des conditions
                            et de les accepter.</label>
        </div>

        <div class="alert alert-info d-flex align-items-center" role="alert">
            <div class="flex-shrink-0">
                <i class="fa fa-fw fa-info-circle"></i>
            </div>
            <div class="flex-grow-1 ms-3">
                <p class="mb-0">
                    N.B: Avant de soumettre votre inscription, veuillez bien vérifier que tous les champs
                    obligatoires sont remplis et que tous vos pièces jointes sont téléchargées.
                </p>
            </div>
        </div>
    </div>
            @endif
        </div>
    </div>
</form>

<!-- Document preview modal -->
<div class="modal fade" id="documentPreviewModal" tabindex="-1" wire:ignore.self>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if($previewDocument)
                    @if(in_array($previewDocument->getClientOriginalExtension(), ['jpg', 'jpeg', 'png']))
                        <img src="{{ $previewDocument->temporaryUrl() }}" class="img-fluid" alt="Aperçu">
                    @else
                        <div class="text-center">
                            <i class="fa fa-file-pdf fa-5x text-danger mb-3"></i>
                            <p class="mb-0">{{ $previewDocument->getClientOriginalName() }}</p>
                        </div>
                    @endif
                @endif
            </div>
        </div>
    </div>
</div>

{{-- <div wire:loading wire:target="register, nextStep, previousStep" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="bg-white p-4 rounded shadow-lg text-center">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
        <h5 class="mb-0">Traitement en cours...</h5>
        <p class="text-muted mb-0">Veuillez patienter un instant</p>
    </div>
</div> --}}

<!-- File upload loading overlay -->
<div wire:loading wire:target="relevebacc, cv, diplome, releve1, releve2, releve3, releve4" class="position-relative">
    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.8); z-index: 999;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Téléchargement en cours...</span>
        </div>
    </div>
</div>

@if (session()->has('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fa fa-check-circle me-1"></i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if (session()->has('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fa fa-exclamation-circle me-1"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@push('scripts')
<script>
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Document preview
    window.addEventListener('showDocumentPreview', event => {
        var modal = new bootstrap.Modal(document.getElementById('documentPreviewModal'));
        modal.show();
    });

    // Animation des étapes
    document.addEventListener('livewire:load', function () {
        Livewire.hook('message.processed', (message, component) => {
            const steps = document.querySelectorAll('.step');
            steps.forEach(step => {
                step.classList.add('fade-enter');
                setTimeout(() => {
                    step.classList.remove('fade-enter');
                }, 300);
            });
        });
    });

    // Validation animations
    Livewire.on('validationError', () => {
        const invalidFields = document.querySelectorAll('.is-invalid');
        invalidFields.forEach(field => {
            field.classList.add('shake');
            setTimeout(() => {
                field.classList.remove('shake');
            }, 500);
        });
    });
</script>
@endpush

<style>
.progress-steps {
    position: relative;
    padding: 0 20px;
}

.step {
    text-align: center;
    position: relative;
    z-index: 1;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    transition: all 0.3s ease;
}

.step.completed .step-icon {
    background: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.step-label {
    font-size: 0.875rem;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step.completed .step-label {
    color: #0d6efd;
    font-weight: 500;
}

.fade-enter {
    opacity: 0;
    transform: translateY(10px);
}

.fade-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms, transform 300ms;
}

.fade-exit {
    opacity: 1;
    transform: translateY(0);
}

.fade-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 300ms, transform 300ms;
}

.step {
    transition: all 0.3s ease;
}

.step:hover {
    transform: translateY(-2px);
}

.step.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: none;
}

.document-preview {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.document-preview:hover {
    transform: scale(1.02);
}

.document-preview img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.document-preview .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.document-preview:hover .overlay {
    opacity: 1;
}

.validation-message {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.validation-message.valid {
    color: #198754;
}

.validation-message.invalid {
    color: #dc3545;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.form-content {
    min-height: 400px;
}

/* Loading states */
.btn:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading overlay animations */
.position-fixed {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.bg-white {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
</style>
