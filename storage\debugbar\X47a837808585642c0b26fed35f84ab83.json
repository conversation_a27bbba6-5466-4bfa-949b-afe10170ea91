{"__meta": {"id": "X47a837808585642c0b26fed35f84ab83", "datetime": "2025-07-01 18:09:53", "utime": 1751382593.911685, "method": "POST", "uri": "/livewire/upload-file?expires=1751382892&signature=1aafff0289b67341e1e3e934ca3d2a897403eeaec215aa9616b182b668328e70", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751382593.401549, "end": 1751382593.911703, "duration": 0.5101540088653564, "duration_str": "510ms", "measures": [{"label": "Booting", "start": 1751382593.401549, "relative_start": 0, "end": 1751382593.749876, "relative_end": 1751382593.749876, "duration": 0.3483269214630127, "duration_str": "348ms", "params": [], "collector": null}, {"label": "Application", "start": 1751382593.750576, "relative_start": 0.3490269184112549, "end": 1751382593.911705, "relative_end": 1.9073486328125e-06, "duration": 0.16112899780273438, "duration_str": "161ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26437352, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/upload-file", "controller": "Livewire\\Controllers\\FileUploadHandler@handle", "as": "livewire.upload-file", "middleware": "web", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php&line=19\">\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php:19-28</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/livewire/preview-file/EHev7TS8QV4Vo66i6yBjzRkKCs3k3W-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384367&signature=3aad8f71066633017471e69926f4b6a320c0ca2ab22fd555edade39724d7c2d6\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:16 [\n    \"sexe\" => \"F\"\n    \"date_naissance\" => \"1995-08-07\"\n    \"lieu_naissance\" => \"In rerum recusandae\"\n    \"adresse\" => \"Voluptates ut tempor\"\n    \"nationalite\" => \"Sed aut odio volupta\"\n    \"telephone2\" => \"+18795584611\"\n    \"cin\" => \"123412341234\"\n    \"date_delivrance\" => \"2020-01-24\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"nom\" => \"Aphrodite\"\n    \"prenom\" => \"Macdonald\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261 32 55 516 50\"\n    \"reference\" => \"21324\"\n    \"telenvoi\" => \"123412\"\n    \"montantenvoi\" => \"12341234\"\n  ]\n  \"niveau\" => \"2\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n]"}, "request": {"path_info": "/livewire/upload-file", "status_code": "<pre class=sf-dump id=sf-dump-1106252151 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1106252151\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-973413375 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1751382892</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">1aafff0289b67341e1e3e934ca3d2a897403eeaec215aa9616b182b668328e70</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-973413375\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1085191595 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1085191595\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-772037183 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">4172080</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarymNnijpWaAmGPuzrv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkJlY053cXY0STFZY2gvMHg2UFBOeVE9PSIsInZhbHVlIjoiT0FQcFE1dXBkVWJveXJnYkdkWTB2cFlIRzZBV29qdTN5d21iN1Y2N1h5UkJLUnlRaWZLZi9WZndldlRHVHB0d2VDcnI3RkQzTGdScTRxS2R2RHhyTXpLWVVid3Z4bEFrb2JzckFuaHVjbmRUTjdyRFRidmdHbWZwLzAvN3FMTk4iLCJtYWMiOiJmOWRmZWVhNDUxZDZlOTdmMzk2ZTgyNmY0MGJlYjcyNzY3NTY3M2IwMGY5ZjEzYjliZmUyZDg5ZTgxMGMxMTBkIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IjJkbVJNc0NGWndWWFdQaW1zNFpXK1E9PSIsInZhbHVlIjoiU2FFQVlYQ0pMTDg2ditXZ0VqSjdBekx2N0Nyb0VyY29BUmJPdE5qRERSZU1WZ2Z3OFh4bytRd21mdDJGMU9xbVpmdVNYdUgrNWVONEROQTZ3SHluTk85eUt2d2VvdUNlcFFFR20xaG5ULytkU2gwRXNnb3d2WUlibHNzMEZMWnciLCJtYWMiOiJjNTk0YjNhMzhiOTMyNzVkMjc3YjZkNGU2ODU0OTFhMGE1ZDdkNjdmODMwODQzM2ZmMGNlZWQyM2EzNjQ2MzY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772037183\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1748564888 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59641</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"115 characters\">/livewire/upload-file?expires=1751382892&amp;signature=1aafff0289b67341e1e3e934ca3d2a897403eeaec215aa9616b182b668328e70</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"93 characters\">expires=1751382892&amp;signature=1aafff0289b67341e1e3e934ca3d2a897403eeaec215aa9616b182b668328e70</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"7 characters\">4172080</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"7 characters\">4172080</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarymNnijpWaAmGPuzrv</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarymNnijpWaAmGPuzrv</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkJlY053cXY0STFZY2gvMHg2UFBOeVE9PSIsInZhbHVlIjoiT0FQcFE1dXBkVWJveXJnYkdkWTB2cFlIRzZBV29qdTN5d21iN1Y2N1h5UkJLUnlRaWZLZi9WZndldlRHVHB0d2VDcnI3RkQzTGdScTRxS2R2RHhyTXpLWVVid3Z4bEFrb2JzckFuaHVjbmRUTjdyRFRidmdHbWZwLzAvN3FMTk4iLCJtYWMiOiJmOWRmZWVhNDUxZDZlOTdmMzk2ZTgyNmY0MGJlYjcyNzY3NTY3M2IwMGY5ZjEzYjliZmUyZDg5ZTgxMGMxMTBkIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IjJkbVJNc0NGWndWWFdQaW1zNFpXK1E9PSIsInZhbHVlIjoiU2FFQVlYQ0pMTDg2ditXZ0VqSjdBekx2N0Nyb0VyY29BUmJPdE5qRERSZU1WZ2Z3OFh4bytRd21mdDJGMU9xbVpmdVNYdUgrNWVONEROQTZ3SHluTk85eUt2d2VvdUNlcFFFR20xaG5ULytkU2gwRXNnb3d2WUlibHNzMEZMWnciLCJtYWMiOiJjNTk0YjNhMzhiOTMyNzVkMjc3YjZkNGU2ODU0OTFhMGE1ZDdkNjdmODMwODQzM2ZmMGNlZWQyM2EzNjQ2MzY4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751382593.4015</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751382593</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748564888\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-610370341 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hRF2mVuiU5BibRd5o4CekB6Fo0oqlULGaPW0Otn7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610370341\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1302531272 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:09:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjRpMEUzQVJkVHpzdHdrSWY5d2VlbGc9PSIsInZhbHVlIjoiK1JFS0FtYWVuWFJ1Mm02ZjJBMTZiQTVONENwWE1OeTRjRTRzWUFxUFpoS1Fhb3ZoQnB5L2JwdlFCTXN3Z0t0WmlXWmtiNzhCRDJ1bnlidFJ0ZHN5LzhOWDExRlFzR2FURWxqSklVU3RLbGNwK2lHcS9WY2xsRXc0RjF0V0NvZTciLCJtYWMiOiI4OGM1M2Q1OWM2ZjAzNjA3ZDkzOWFmMTAzOGE2NDFiMDJlYTFkMWEzMWM1YjFkODdlZGNjZjhkYTU1OTU3Yzc0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:09:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IjlLUWpLV1VyZDF3VEozbUh2TDM4Rmc9PSIsInZhbHVlIjoiNXZpb2hmN0xnSXA1K3pLZEswcmtRajI5RzduTXFjNEJhdnhta2hIL2dHK0pCajJWVytzMWs0NzFkd2VLSVNzN05zMTR1QTNzczBNd3YvZ2NQdFlGVWxrZ3RXVEJ3QnlkZUpQZFpSbitCNTZFVUdqS3B2WVFTYmlmbkYzZlFnMDciLCJtYWMiOiI3MjlhM2E1MzQ0MTNiNDk4Nzc4NWE0MDYzNDUyNWE2ZmE2Y2NlNDZmOWVmNGZkNzZiNzEzOTc0YTRlZmMzZGUxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:09:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjRpMEUzQVJkVHpzdHdrSWY5d2VlbGc9PSIsInZhbHVlIjoiK1JFS0FtYWVuWFJ1Mm02ZjJBMTZiQTVONENwWE1OeTRjRTRzWUFxUFpoS1Fhb3ZoQnB5L2JwdlFCTXN3Z0t0WmlXWmtiNzhCRDJ1bnlidFJ0ZHN5LzhOWDExRlFzR2FURWxqSklVU3RLbGNwK2lHcS9WY2xsRXc0RjF0V0NvZTciLCJtYWMiOiI4OGM1M2Q1OWM2ZjAzNjA3ZDkzOWFmMTAzOGE2NDFiMDJlYTFkMWEzMWM1YjFkODdlZGNjZjhkYTU1OTU3Yzc0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:09:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IjlLUWpLV1VyZDF3VEozbUh2TDM4Rmc9PSIsInZhbHVlIjoiNXZpb2hmN0xnSXA1K3pLZEswcmtRajI5RzduTXFjNEJhdnhta2hIL2dHK0pCajJWVytzMWs0NzFkd2VLSVNzN05zMTR1QTNzczBNd3YvZ2NQdFlGVWxrZ3RXVEJ3QnlkZUpQZFpSbitCNTZFVUdqS3B2WVFTYmlmbkYzZlFnMDciLCJtYWMiOiI3MjlhM2E1MzQ0MTNiNDk4Nzc4NWE0MDYzNDUyNWE2ZmE2Y2NlNDZmOWVmNGZkNzZiNzEzOTc0YTRlZmMzZGUxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:09:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302531272\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-784504679 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"198 characters\">http://localhost:8000/livewire/preview-file/EHev7TS8QV4Vo66i6yBjzRkKCs3k3W-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384367&amp;signature=3aad8f71066633017471e69926f4b6a320c0ca2ab22fd555edade39724d7c2d6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:16</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-08-07</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">In rerum recusandae</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Voluptates ut tempor</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed aut odio volupta</span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+18795584611</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2020-01-24</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Aphrodite</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Macdonald</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"19 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n      \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"5 characters\">21324</span>\"\n      \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123412</span>\"\n      \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12341234</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>2</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784504679\", {\"maxDepth\":0})</script>\n"}}