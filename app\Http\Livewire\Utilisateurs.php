<?php

namespace App\Http\Livewire;

use App\Mail\SignUp;
use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Mention;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Livewire\WithPagination;

class Utilisateurs extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $newUser = [];
    public $editUser = [];
    public $mediaItems = [];
    public $isEtu = false;
    public $query = '';
    public $newPasswd;

    // Nouvelles propriétés pour l'amélioration UX
    public $filterRole = '';
    public $filterStatus = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedUsers = [];
    public $selectAll = false;

    public $rolePermissions = [];

    // Propriétés pour le modal d'inscription
    public $showInscriptionModal = false;
    public $selectedUserForInscription = [];
    public $newInscription = [
        'annee_universitaire_id' => '',
        'niveau_id' => '',
        'parcour_id' => '',
    ];

    protected $listeners = [
        "selectDate" => 'getSelectedDate',
        "confirmDelete" => 'deleteUser'
    ];

    // Propriétés pour optimiser les performances
    protected $queryString = [
        'query' => ['except' => ''],
        'filterRole' => ['except' => ''],
        'filterStatus' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updatingQuery(){
        $this->resetPage();
    }

    public function updatingFilterRole(){
        $this->resetPage();
    }

    public function updatingFilterStatus(){
        $this->resetPage();
    }

    public function updatingPerPage(){
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->query = '';
        $this->filterRole = '';
        $this->filterStatus = '';
        $this->selectedUsers = [];
        $this->selectAll = false;
        $this->resetPage();
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedUsers = $this->users->pluck('id')->toArray();
        } else {
            $this->selectedUsers = [];
        }
    }

    public function selectAllUsers()
    {
        $this->selectedUsers = $this->users->pluck('id')->toArray();
        $this->selectAll = true;
    }

    public function deselectAll()
    {
        $this->selectedUsers = [];
        $this->selectAll = false;
    }

    public function deleteSelected()
    {
        if (count($this->selectedUsers) === 0) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Aucun utilisateur sélectionné"
            ]);
            return;
        }

        DB::beginTransaction();
        try {
            $count = count($this->selectedUsers);

            // Récupérer les utilisateurs à supprimer
            $users = User::whereIn('id', $this->selectedUsers)->get();

            foreach ($users as $user) {
                // Supprimer les relations
                $user->roles()->detach();
                $user->permissions()->detach();

                // Supprimer les médias associés si la méthode existe
                if (method_exists($user, 'clearMediaCollection')) {
                    $user->clearMediaCollection();
                }
            }

            // Supprimer les utilisateurs
            User::whereIn('id', $this->selectedUsers)->delete();

            DB::commit();

            // Vider le cache
            $this->clearCache();

            // Réinitialiser la sélection
            $this->selectedUsers = [];
            $this->selectAll = false;

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "$count utilisateur(s) supprimé(s) avec succès!"
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de la suppression en lot: " . $e->getMessage()
            ]);
        }
    }



    public function openInscriptionModal($userId)
    {
        $user = User::with(['roles', 'inscriptions' => function($query) {
            $query->with(['parcours', 'niveau', 'annee'])->latest();
        }])->find($userId);

        if (!$user) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Utilisateur introuvable"
            ]);
            return;
        }

        // Vérifier si l'utilisateur est étudiant
        $isStudent = $user->roles->contains(function($role) {
            return stripos($role->nom, 'étudiant') !== false || stripos($role->nom, 'etudiant') !== false;
        });

        if (!$isStudent) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Cette action n'est disponible que pour les étudiants"
            ]);
            return;
        }

        $this->selectedUserForInscription = [
            'id' => $user->id,
            'nom' => $user->nom,
            'prenom' => $user->prenom,
            'email' => $user->email,
            'current_inscriptions' => $user->inscriptions->map(function($inscription) {
                return [
                    'id' => $inscription->id,
                    'parcour' => $inscription->parcours->nom ?? 'N/A',
                    'niveau' => $inscription->niveau->nom ?? 'N/A',
                    'annee' => $inscription->annee->nom ?? 'N/A',
                    'created_at' => $inscription->created_at->format('d/m/Y')
                ];
            })->toArray()
        ];

        $this->newInscription = [
            'annee_universitaire_id' => '',
            'niveau_id' => '',
            'parcour_id' => '',
        ];

        $this->showInscriptionModal = true;
    }

    public function closeInscriptionModal()
    {
        $this->showInscriptionModal = false;
        $this->selectedUserForInscription = [];
        $this->newInscription = [
            'annee_universitaire_id' => '',
            'niveau_id' => '',
            'parcour_id' => '',
        ];
    }

    public function createInscription()
    {
        $this->validate([
            'newInscription.annee_universitaire_id' => 'required|exists:annee_universitaires,id',
            'newInscription.niveau_id' => 'required|exists:niveaux,id',
            'newInscription.parcour_id' => 'required|exists:parcours,id',
        ], [
            'newInscription.annee_universitaire_id.required' => 'L\'année universitaire est obligatoire',
            'newInscription.niveau_id.required' => 'Le niveau est obligatoire',
            'newInscription.parcour_id.required' => 'Le parcours est obligatoire',
        ]);

        try {
            // Vérifier si une inscription existe déjà pour cette année
            $existingInscription = InscriptionStudent::where('user_id', $this->selectedUserForInscription['id'])
                ->where('annee_universitaire_id', $this->newInscription['annee_universitaire_id'])
                ->first();

            if ($existingInscription) {
                $this->dispatchBrowserEvent("showErrorMessage", [
                    "message" => "Une inscription existe déjà pour cette année universitaire"
                ]);
                return;
            }

            // Créer la nouvelle inscription
            InscriptionStudent::create([
                "annee_universitaire_id" => $this->newInscription['annee_universitaire_id'],
                "niveau_id" => $this->newInscription['niveau_id'],
                "parcour_id" => $this->newInscription['parcour_id'],
                "user_id" => $this->selectedUserForInscription['id'],
            ]);

            $this->closeInscriptionModal();

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Inscription créée avec succès!"
            ]);

        } catch (\Exception $e) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de la création de l'inscription: " . $e->getMessage()
            ]);
        }
    }

    public function resetForm()
    {
        $this->newUser = [];
        $this->isEtu = false;
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "Formulaire réinitialisé avec succès!"
        ]);
    }

    public function render()
    {
        Carbon::setLocale("fr");

        if ($this->newUser != []) {
            $this->showParcours();
        }

        // Construction de la requête optimisée avec filtres et tri
        $query = User::query()
            ->select(['id', 'nom', 'prenom', 'email', 'telephone1', 'photo', 'matricule', 'created_at', 'updated_at'])
            ->with(['roles:id,nom']);

        // Filtrage par recherche avec index optimisé
        if ($this->query) {
            $searchTerm = '%' . $this->query . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('nom', 'like', $searchTerm)
                  ->orWhere('prenom', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm)
                  ->orWhere('matricule', 'like', $searchTerm);
            });
        }

        // Filtrage par rôle optimisé
        if ($this->filterRole) {
            $query->whereHas('roles', function($q) {
                $q->where('roles.id', $this->filterRole);
            });
        }

        // Filtrage par statut (basé sur l'existence d'un email et d'un mot de passe)
        if ($this->filterStatus === 'active') {
            $query->whereNotNull('email')->whereNotNull('password');
        } elseif ($this->filterStatus === 'inactive') {
            $query->where(function($q) {
                $q->whereNull('email')->orWhereNull('password');
            });
        }

        // Tri optimisé
        $query->orderBy($this->sortField, $this->sortDirection);

        // Pagination avec cache
        $users = $query->paginate($this->perPage);

        // Calcul des statistiques avec cache
        $stats = $this->getStatistics();

        return view('livewire.utilisateurs.index', [
            "users" => $users,
            "roles" => $this->getRoles(),
            "parcours" => $this->getParcours(),
            "mentions" => $this->getMentions(),
            "niveaux" => $this->getNiveaux(),
            "annees" => $this->getAnnees(),
            "totalUsers" => $stats['total'],
            "activeUsers" => $stats['active'],
            "studentsCount" => $stats['students'],
            "staffCount" => $stats['staff']
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    // Méthodes optimisées avec cache
    private function getStatistics()
    {
        return cache()->remember('user_statistics', 300, function () {
            $total = User::count();
            $active = User::whereNotNull('email')->whereNotNull('password')->count();
            $students = User::whereHas('roles', function($q) {
                $q->where('nom', 'like', '%étudiant%')->orWhere('nom', 'like', '%Etudiant%');
            })->count();

            return [
                'total' => $total,
                'active' => $active,
                'students' => $students,
                'staff' => $total - $students
            ];
        });
    }

    private function getRoles()
    {
        return cache()->remember('all_roles', 3600, function () {
            return Role::select(['id', 'nom', 'created_at'])->get();
        });
    }

    private function getParcours()
    {
        return cache()->remember('all_parcours', 3600, function () {
            return Parcour::select(['id', 'nom'])->get();
        });
    }

    private function getMentions()
    {
        return cache()->remember('all_mentions', 3600, function () {
            return Mention::select(['id', 'nom'])->get();
        });
    }

    private function getNiveaux()
    {
        return cache()->remember('all_niveaux', 3600, function () {
            return Niveau::select(['id', 'nom'])->get();
        });
    }

    private function getAnnees()
    {
        return cache()->remember('all_annees', 3600, function () {
            return AnneeUniversitaire::select(['id', 'nom'])->get();
        });
    }

    public function goToListUser()
    {
        $this->currentPage = PAGELIST;
        $this->editUser = [];
        // return redirect(request()->header('Referer'));
    }

    public function showParcours()
    {
        if ($this->newUser["role"] == 5) {
            $this->isEtu = true;
        }
        elseif ($this->newUser["role"] != 5) {
            $this->isEtu = false;
        }
        
    }


    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {

            // 'required|email|unique:users,email Rule::unique("users", "email")->ignore($this->editUser['id'])
            return [
                'editUser.nom' => 'required',
                'editUser.prenom' => 'required',
                'editUser.email' => ['required', 'email', Rule::unique("users", "email")->ignore($this->editUser['id'])],
                'editUser.telephone1' => ['required', 'numeric', Rule::unique("users", "telephone1")->ignore($this->editUser['id'])],
                'editUser.sexe' => 'required',
                'editUser.parcour_id' => '',
                'editUser.niveau_id' => '',
            ];
        }

        return [
            'newUser.nom' => 'required',
            'newUser.prenom' => 'required',
            'newUser.email' => 'required|email|unique:users,email',
            'newUser.sexe' => 'required',
            'newUser.matricule' => 'unique:users,matricule',
            'newUser.telephone1' => 'required|numeric|unique:users,telephone1',
            'newUser.date_naissance' => 'required|before:01/01/14',
            'newUser.lieu_naissance' => 'required',
            'newUser.nationalite' => 'required',
            'newUser.ville' => 'required',
            'newUser.pays' => 'required',
            'newUser.adresse' => 'required',
            'newUser.telephone1' => 'required',
            'newUser.cin' => 'unique:users,cin',

        ];
    }
    
    public function goToAddUser()
    {
        $this->dispatchBrowserEvent("helperDatePicker");
        $this->currentPage = PAGECREATEFORM;
    }

    public function goTest()
    {

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur créé avec succès!"]);
    }

    public function goToEditUser($id)
    {
        $user = User::find($id);
        $this->editUser = $user->toArray();
        // $mapForCB = function ($value) {
        //     return $value["id"];
        // };
        $this->mediaItems = $user->getMedia("*");

        // $this->editUser["role"] = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray());
        $this->currentPage = PAGEEDITFORM;
        //  dd($this->editUser);
        $this->populateRolePermissions();
    }

    public function populateRolePermissions()
    {
        $this->rolePermissions["roles"] = [];
        $this->rolePermissions["permissions"] = [];

        $mapForCB = function ($value) {
            return $value["id"];
        };

        $roleIds = array_map($mapForCB, User::find($this->editUser["id"])->roles->toArray()); // [1, 2, 4]
        $permissionIds = array_map($mapForCB, User::find($this->editUser["id"])->permissions->toArray()); // [1, 2, 4]

        foreach (Role::all() as $role) {
            if (in_array($role->id, $roleIds)) {
                array_push($this->rolePermissions["roles"], ["role_id" => $role->id, "role_nom" => $role->nom, "active" => true]);
            } else {
                array_push($this->rolePermissions["roles"], ["role_id" => $role->id, "role_nom" => $role->nom, "active" => false]);
            }
        }

        // foreach (Permission::all() as $permission) {
        //     if (in_array($permission->id, $permissionIds)) {
        //         array_push($this->rolePermissions["permissions"], ["permission_id" => $permission->id, "permission_nom" => $permission->nom, "active" => true]);
        //     } else {
        //         array_push($this->rolePermissions["permissions"], ["permission_id" => $permission->id, "permission_nom" => $permission->nom, "active" => false]);
        //     }
        // }


        // la logique pour charger les roles et les permissions
    }

    public function updateRoleAndPermissions()
    {
        
        DB::table("role_user")->where("user_id", $this->editUser["id"])->delete();
        // DB::table("user_permission")->where("user_id", $this->editUser["id"])->delete();

        foreach ($this->rolePermissions["roles"] as $role) {
            if ($role["active"]) {
                User::find($this->editUser["id"])->roles()->attach($role["role_id"]);
            }
        }

        // foreach ($this->rolePermissions["permissions"] as $permission) {
        //     if ($permission["active"]) {
        //         User::find($this->editUser["id"])->permissions()->attach($permission["permission_id"]);
        //     }
        // }

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Roles et permissions mis à jour avec succès!"]);
    }

    public function getSelectedDate($date)
    {
        $this->newUser["date_naissance"] = $date;
    }

    

    public function addUser()
    {
        $pass = Str::random(10);
        $role = $this->newUser["role"];

        // Validation optimisée
        $validationAttributes = $this->validate();

        $validationAttributes["newUser"]["photo"] = "media/avatars/avatar0.jpg";
        $validationAttributes["newUser"]["password"] = Hash::make($pass);

        // Transaction pour assurer la cohérence
        DB::beginTransaction();
        try {
            // Créer l'utilisateur
            $user = User::create($validationAttributes["newUser"]);

            // Attacher le rôle si spécifié
            if ($role) {
                $user->roles()->attach($role);
            }

            // Envoyer l'email de bienvenue
            Mail::to($this->newUser["email"])->send(new SignUp(
                $this->newUser["nom"],
                $this->newUser["prenom"],
                $pass,
                $this->newUser["email"]
            ));

            DB::commit();

            // Vider le cache des statistiques
            $this->clearCache();

            $this->newUser = [];
            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Utilisateur créé avec succès! Un email avec les identifiants a été envoyé."
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de la création de l'utilisateur: " . $e->getMessage()
            ]);
        }
    }

    public function updateUser()
    {
        // Validation optimisée
        $validationAttributes = $this->validate();

        DB::beginTransaction();
        try {
            $user = User::find($this->editUser["id"]);

            if (!$user) {
                throw new \Exception("Utilisateur introuvable");
            }

            // Mise à jour des informations de base
            $user->update($validationAttributes["editUser"]);

            // Mise à jour des informations académiques si présentes
            if (isset($this->editUser['parcour_id']) || isset($this->editUser['niveau_id']) || isset($this->editUser['annee_universitaire_id'])) {
                InscriptionStudent::updateOrCreate(
                    ['user_id' => $user->id],
                    [
                        "parcour_id" => $this->editUser['parcour_id'] ?? null,
                        "niveau_id" => $this->editUser['niveau_id'] ?? null,
                        "annee_universitaire_id" => $this->editUser['annee_universitaire_id'] ?? null,
                    ]
                );
            }

            DB::commit();

            // Vider le cache
            $this->clearCache();

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Utilisateur mis à jour avec succès!"
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de la mise à jour: " . $e->getMessage()
            ]);
        }
    }

    public function confirmDelete($name, $id)
    {
        $this->dispatchBrowserEvent("showConfirmMessage", ["message" => [
            "text" => "Vous êtes sur le point de supprimer <strong>$name</strong> de la liste des utilisateurs. Cette action est irréversible.",
            "title" => "Confirmer la suppression",
            "type" => "warning",
            "confirmButtonText" => "Oui, supprimer",
            "cancelButtonText" => "Annuler",
            "data" => [
                "user_id" => $id
            ]
        ]]);
    }

    public function deleteUser($id)
    {
        DB::beginTransaction();
        try {
            $user = User::find($id);

            if (!$user) {
                throw new \Exception("Utilisateur introuvable");
            }

            // Supprimer les relations
            $user->roles()->detach();
            $user->permissions()->detach();

            // Supprimer les médias associés si la méthode existe
            if (method_exists($user, 'clearMediaCollection')) {
                $user->clearMediaCollection();
            }

            // Supprimer l'utilisateur
            $user->delete();

            DB::commit();

            // Vider le cache
            $this->clearCache();

            // Retirer de la sélection si présent
            $this->selectedUsers = array_diff($this->selectedUsers, [$id]);

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Utilisateur supprimé avec succès!"
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de la suppression: " . $e->getMessage()
            ]);
        }
    }

    public function resetPassword()
    {
        if (empty($this->newPasswd)) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Veuillez saisir un nouveau mot de passe"
            ]);
            return;
        }

        try {
            $user = User::find($this->editUser["id"]);

            if (!$user) {
                throw new \Exception("Utilisateur introuvable");
            }

            $user->update(["password" => Hash::make($this->newPasswd)]);

            // Réinitialiser le champ
            $this->newPasswd = '';

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Mot de passe utilisateur réinitialisé avec succès!"
            ]);

        } catch (\Exception $e) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de la réinitialisation: " . $e->getMessage()
            ]);
        }
    }

    // Méthode pour vider le cache
    private function clearCache()
    {
        cache()->forget('user_statistics');
        cache()->forget('all_roles');
        cache()->forget('all_parcours');
        cache()->forget('all_mentions');
        cache()->forget('all_niveaux');
        cache()->forget('all_annees');
    }

    // Optimisations Livewire
    public function dehydrate()
    {
        // Nettoyer les données non nécessaires avant la sérialisation
        $this->dispatchBrowserEvent('livewire:dehydrated');
    }

    public function hydrate()
    {
        // Réinitialiser les données après la désérialisation
        Carbon::setLocale("fr");
    }

    // Méthode pour précharger les données
    public function mount()
    {
        // Précharger les données fréquemment utilisées
        $this->getRoles();
        $this->getParcours();
        $this->getMentions();
        $this->getNiveaux();
        $this->getAnnees();
    }

    // Utiliser la vue de pagination par défaut de Laravel
    // public function paginationView()
    // {
    //     return 'pagination::bootstrap-4';
    // }

    // Méthode pour optimiser les requêtes de recherche
    private function optimizeSearchQuery($query, $searchTerm)
    {
        // Utiliser FULLTEXT search si disponible, sinon LIKE
        if (config('database.default') === 'mysql') {
            return $query->whereRaw(
                "MATCH(nom, prenom, email) AGAINST(? IN BOOLEAN MODE)",
                [$searchTerm . '*']
            );
        }

        return $query->where(function($q) use ($searchTerm) {
            $q->where('nom', 'like', '%' . $searchTerm . '%')
              ->orWhere('prenom', 'like', '%' . $searchTerm . '%')
              ->orWhere('email', 'like', '%' . $searchTerm . '%');
        });
    }

    // Méthode pour débouncer les recherches
    public function updatedQuery()
    {
        $this->resetPage();
        // Débouncer la recherche pour éviter trop de requêtes
        $this->dispatchBrowserEvent('search-debounced');
    }

    // Méthode pour exporter les données de manière optimisée
    public function exportUsers()
    {
        try {
            // Utiliser un job en arrière-plan pour les gros exports
            if (User::count() > 1000) {
                // Dispatch job
                $this->dispatchBrowserEvent("showSuccessMessage", [
                    "message" => "Export en cours... Vous recevrez un email quand il sera prêt."
                ]);
            } else {
                // Export direct pour les petites données
                $this->dispatchBrowserEvent("showSuccessMessage", [
                    "message" => "Export terminé!"
                ]);
            }
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de l'export: " . $e->getMessage()
            ]);
        }
    }
}
