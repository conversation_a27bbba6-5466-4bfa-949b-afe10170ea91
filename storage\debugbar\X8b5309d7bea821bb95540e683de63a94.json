{"__meta": {"id": "X8b5309d7bea821bb95540e683de63a94", "datetime": "2025-07-01 18:21:29", "utime": 1751383289.944162, "method": "GET", "uri": "/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png?expires=1751385088&signature=61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751383289.563189, "end": 1751383289.944211, "duration": 0.38102197647094727, "duration_str": "381ms", "measures": [{"label": "Booting", "start": 1751383289.563189, "relative_start": 0, "end": 1751383289.874965, "relative_end": 1751383289.874965, "duration": 0.31177592277526855, "duration_str": "312ms", "params": [], "collector": null}, {"label": "Application", "start": 1751383289.87556, "relative_start": 0.31237101554870605, "end": 1751383289.944213, "relative_end": 1.9073486328125e-06, "duration": 0.06865286827087402, "duration_str": "68.65ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 24329296, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/preview-file/{filename}", "controller": "Livewire\\Controllers\\FilePreviewHandler@handle", "as": "livewire.preview-file", "middleware": "web", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\FilePreviewHandler.php&line=11\">\\vendor\\livewire\\livewire\\src\\Controllers\\FilePreviewHandler.php:11-19</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png?expires=1751385088&signature=61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:5 [\n  \"newUser\" => array:10 [\n    \"nom\" => \"qdfqs\"\n    \"prenom\" => \"qsdfqsdf\"\n    \"date_naissance\" => \"2002-01-03\"\n    \"sexe\" => \"H\"\n    \"lieu_naissance\" => \"qsdf\"\n    \"adresse\" => \"qsdf\"\n    \"nationalite\" => \"qsdf\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261 32 55 516 50\"\n    \"cin\" => \"123412341234\"\n  ]\n  \"niveau\" => \"1\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n  \"files_uploaded\" => array:7 [\n    \"cv\" => false\n    \"diplome\" => false\n    \"relevebacc\" => true\n    \"releve1\" => false\n    \"releve2\" => false\n    \"releve3\" => false\n    \"releve4\" => false\n  ]\n]"}, "request": {"path_info": "/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png", "status_code": "<pre class=sf-dump id=sf-dump-1608312680 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1608312680\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1009429542 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1751385088</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009429542\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1877183160 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1877183160\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-727323855 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ikh1c3p5SHhyK1lhVUV6SXZraWxMRkE9PSIsInZhbHVlIjoiRFNLSkh4Nk1RMjlFSUdHRngxZWo5YlRKd0tPNWhXenB2QXFNYkdJZ3Eva0RrTldFSDFwUHZzOWRYcCtCODFMS1FXT05LTXdtY2c1NzVENGV4UFFPdWxreE5ud1ljZDl0WDNTVEZEc1RsM25JZVcxT3g2TUFCcm1iQ2xCTzhtT1ciLCJtYWMiOiIwMTczZTVjZjkwMmNlZDJjMTk2MmQwZDBmOGU4YmZlZWJjNzdiZTNlYThmNzJmNDIxYTlmNWNmN2U5YzEyYzNiIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6InlIRTljNFVPei9rZUlVTmhjdldYL0E9PSIsInZhbHVlIjoibFJoTnkrQnFWeU5SSlNKN0VIb2p6blp3YnRGNmxhditxZEs5TVlyb0NSWFZxWi8waHkvRVdHYm1wWm5VNE9PRWtQeDFVSEc5NVZuNUVGVUtRaWhhc0lzbERXelY0Y2d0WEo0bWxmNDRGN20wN3JiNER5bWw5c1k5WWpCdXNXbjUiLCJtYWMiOiIzZWIzMzU2MDBlNDQyZDI5YzljOWU0MWE2NjczOGQzZWEwODYxYTNkNDk1YmI3YzU5ODlhY2E0OGMxNzEyMWRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727323855\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1893690225 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60457</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"165 characters\">/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png?expires=1751385088&amp;signature=61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"71 characters\">/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"108 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console/../resources/server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"71 characters\">/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"93 characters\">expires=1751385088&amp;signature=61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ikh1c3p5SHhyK1lhVUV6SXZraWxMRkE9PSIsInZhbHVlIjoiRFNLSkh4Nk1RMjlFSUdHRngxZWo5YlRKd0tPNWhXenB2QXFNYkdJZ3Eva0RrTldFSDFwUHZzOWRYcCtCODFMS1FXT05LTXdtY2c1NzVENGV4UFFPdWxreE5ud1ljZDl0WDNTVEZEc1RsM25JZVcxT3g2TUFCcm1iQ2xCTzhtT1ciLCJtYWMiOiIwMTczZTVjZjkwMmNlZDJjMTk2MmQwZDBmOGU4YmZlZWJjNzdiZTNlYThmNzJmNDIxYTlmNWNmN2U5YzEyYzNiIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6InlIRTljNFVPei9rZUlVTmhjdldYL0E9PSIsInZhbHVlIjoibFJoTnkrQnFWeU5SSlNKN0VIb2p6blp3YnRGNmxhditxZEs5TVlyb0NSWFZxWi8waHkvRVdHYm1wWm5VNE9PRWtQeDFVSEc5NVZuNUVGVUtRaWhhc0lzbERXelY0Y2d0WEo0bWxmNDRGN20wN3JiNER5bWw5c1k5WWpCdXNXbjUiLCJtYWMiOiIzZWIzMzU2MDBlNDQyZDI5YzljOWU0MWE2NjczOGQzZWEwODYxYTNkNDk1YmI3YzU5ODlhY2E0OGMxNzEyMWRiIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751383289.5632</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751383289</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893690225\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1006660877 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3MQqMzZAJmil6GWNRTwZQGoMQJ2F3HRZ41OmdrAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006660877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">image/png; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 15:21:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:21:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:21:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">93891</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik91d3loOTZSZDByV3F4bGU2cFZxWlE9PSIsInZhbHVlIjoiMUhjVGEySUtNTHVkT0NSUllBKzRNVVg0czVaSGFkQ0ZpMGorSTNZV3dLeW9ndGROTXBsSzc4QU1aakg2MG8wcnRNR2U0U0Z4UWpUYXVjRmY2ZmNUTnRoZnZNTnVBbEtwNDRMcDFDYXVJMmxlaVlsMkNDTko0WlgyK3l1NG12QzQiLCJtYWMiOiI2YjZhNjM4ZDMwYzA1MzA4MGQ5M2FhYmQyMmIwMTg3Y2QyYTVlN2M2NmYxNzc5OTQ5Mjk1NGE4MjY2ZjI0ZDA1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:21:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6ImtKeVNIWE1vbWFTaXIxUjlJVmE4L1E9PSIsInZhbHVlIjoiK3NWdGVnOFlxUXVQazJ3UFhPZFgyeS9Gc3FZeklpTk9IbHVjSnZ1NXRkQzFQRUgyVTkvQi9GOVI0WE1zQ0lOdW96TmN2ejEyS1EwN1BEUGhMbW9wYWFjU0tWZmNSQXVoZTB3WkN5WGdMUk1xOHA2MWtmUmsyREp1NEs1MHAwS2IiLCJtYWMiOiI1YThhMGMyNDFhZWFmYTIyZWFjNDliNTliMDAyY2Y3Yzc4ZjlkZjY1Njk5MGQ4Mzc3YWYyOTE5YzM1YTdjYTNmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:21:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik91d3loOTZSZDByV3F4bGU2cFZxWlE9PSIsInZhbHVlIjoiMUhjVGEySUtNTHVkT0NSUllBKzRNVVg0czVaSGFkQ0ZpMGorSTNZV3dLeW9ndGROTXBsSzc4QU1aakg2MG8wcnRNR2U0U0Z4UWpUYXVjRmY2ZmNUTnRoZnZNTnVBbEtwNDRMcDFDYXVJMmxlaVlsMkNDTko0WlgyK3l1NG12QzQiLCJtYWMiOiI2YjZhNjM4ZDMwYzA1MzA4MGQ5M2FhYmQyMmIwMTg3Y2QyYTVlN2M2NmYxNzc5OTQ5Mjk1NGE4MjY2ZjI0ZDA1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:21:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6ImtKeVNIWE1vbWFTaXIxUjlJVmE4L1E9PSIsInZhbHVlIjoiK3NWdGVnOFlxUXVQazJ3UFhPZFgyeS9Gc3FZeklpTk9IbHVjSnZ1NXRkQzFQRUgyVTkvQi9GOVI0WE1zQ0lOdW96TmN2ejEyS1EwN1BEUGhMbW9wYWFjU0tWZmNSQXVoZTB3WkN5WGdMUk1xOHA2MWtmUmsyREp1NEs1MHAwS2IiLCJtYWMiOiI1YThhMGMyNDFhZWFmYTIyZWFjNDliNTliMDAyY2Y3Yzc4ZjlkZjY1Njk5MGQ4Mzc3YWYyOTE5YzM1YTdjYTNmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:21:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"186 characters\">http://localhost:8000/livewire/preview-file/8JJqJSLuUQynbiw31PB1fZsJNsSQq1-metaMi5wbmc=-.png?expires=1751385088&amp;signature=61b975f476ad03edc6f7e3cd674b7e38dd709867989998ddd2ed483e0d055796</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">qdfqs</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">qsdfqsdf</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2002-01-03</span>\"\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n    \"<span class=sf-dump-key>files_uploaded</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>cv</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>diplome</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>relevebacc</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>releve1</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>releve2</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>releve3</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>releve4</span>\" => <span class=sf-dump-const>false</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}