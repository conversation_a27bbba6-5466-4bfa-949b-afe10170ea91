 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Paiment pour <?php echo e($current_user->nom); ?> <?php echo e($current_user->prenom); ?> <?php echo e($current_annee->nom); ?> | ID :
             <?php echo e($current_user->id); ?>

         </h1>

     </div>
 </div>
 <!-- END Hero -->


 <div class="content">
     <div class="row">
         <?php $__currentLoopData = $newPay; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
             <div class="col-md-12">
                 <!-- Basic -->
                 <div class="block block-rounded block-themed">
                     <div class="block-header block-header-default">
                         <h3 class="block-title text-center"><?php echo e($payment['type_nom']); ?></h3>

                     </div>
                     <div class="block-content">
                         <?php
                             $total = 0;
                             $histos = \App\Models\HistoriquePayment::with("moyen")->where('user_id', $current_user->id)
                                 ->where('annee_universitaire_id', $current_annee->id)
                                 ->where('type_payment_id', $payment['type_id'])->get();
                         ?>

                         <?php if($histos->isNotEmpty()): ?>
                             <div class="table-responsive">
                                 <table class="table table-bordered table-striped table-vcenter">
                                     <thead>
                                         <tr>
                                             <th class="text-center">Moyen</th>
                                             <th class="text-center">Code</th>
                                             <th class="text-center">Montant</th>
                                             <th class="text-center">Observation</th>
                                         </tr>
                                     </thead>
                                     <tbody>
                                         <?php $__currentLoopData = $histos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $histo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                             <?php
                                                 $total += $histo->montant;
                                             ?>
                                             <tr>
                                                 <td class="text-center">
                                                     <?php echo e($histo->moyen->nom); ?>

                                                 </td>
                                                 <td class="text-center">
                                                     <?php echo e($histo->code); ?>

                                                 </td>
                                                 <td class="text-center">
                                                     <?php echo e($histo->prixForHumans); ?>

                                                 </td>
                                                 <td class="text-center">
                                                     <?php echo e($histo->created_at); ?>

                                                 </td>
                                             </tr>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                 </table>
                                 <?php
                                     $t = number_format($total, 0, ',', ' ') . ' ' . env('CURRENCY', 'Ar');
                                 ?>
                                 <p class="text-end h5 <?php if($payment["max"] != $total): ?> text-danger <?php endif; ?>">Total payé
                                     : <?php echo e($t); ?></p>

                             </div>

                             <?php if($payment["max"] != $total): ?>
                                 <p class="text-center h5">Payment du reste</p>
                                 <div class="row mb-3">
                                     <div class="col-md-3">
                                         <select
                                             class="form-select <?php $__errorArgs = ['newPay.' . $loop->index . '.moyen'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                             wire:model="newPay.<?php echo e($loop->index); ?>.moyen" id="example-select1"
                                             name="example-select1">
                                             <option selected value="null">Moyen de paiment</option>
                                             <?php $__currentLoopData = $moyens; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moyen): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                 <option value="<?php echo e($moyen->id); ?>"><?php echo e($moyen->nom); ?>

                                                 </option>
                                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         </select>

                                         <?php $__errorArgs = ['newPay.' . $loop->index . '.moyen'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                             <span class="text-danger"><?php echo e($message); ?></span>
                                         <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                     </div>
                                     <div class="col-md-3">
                                         <input type="text" wire:model="newPay.<?php echo e($loop->index); ?>.code"
                                             class="form-control <?php $__errorArgs = ['newPay.' . $loop->index . '.code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                             id="example-text-input" name="example-text-input"
                                             placeholder="Entrez le code du reçu">

                                         <?php $__errorArgs = ['newPay.' . $loop->index . '.code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                             <span class="text-danger"><?php echo e($message); ?></span>
                                         <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                     </div>
                                     <div class="col-md-3">
                                         <input type="number" wire:model="newPay.<?php echo e($loop->index); ?>.montant"
                                             class="form-control <?php $__errorArgs = ['newPay.' . $loop->index . '.montant'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                             id="example-text-input" name="address" placeholder="Entrez le montant">

                                         <?php $__errorArgs = ['newPay.' . $loop->index . '.montant'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                             <span class="text-danger"><?php echo e($message); ?></span>
                                         <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                     </div>
                                     <div class="col-md-3">
                                         <button type="button" wire:click="valider(<?php echo e($loop->index); ?>)"
                                             class="btn btn-sm btn-alt-primary" <?php if(empty($newPay[$loop->index])): echo 'disabled'; endif; ?>>Valider
                                         </button>
                                     </div>
                                 </div>
                             <?php endif; ?>
                         <?php else: ?>
                             
                             <div class="row m-3">
                                 <div class="col-md-3">
                                     <select
                                         class="form-select <?php $__errorArgs = ['newPay.' . $loop->index . '.moyen'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                         wire:model="newPay.<?php echo e($loop->index); ?>.moyen" id="example-select1"
                                         name="example-select1">
                                         <option selected value="null">Moyen de paiment</option>
                                         <?php $__currentLoopData = $moyens; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moyen): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                             <option value="<?php echo e($moyen->id); ?>"><?php echo e($moyen->nom); ?>

                                             </option>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                     </select>
                                 </div>
                                 <div class="col-md-3">
                                     <input type="text" wire:model="newPay.<?php echo e($loop->index); ?>.code"
                                         class="form-control <?php $__errorArgs = ['newPay.' . $loop->index . '.code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                         id="example-text-input" name="example-text-input"
                                         placeholder="Entrez le code du reçu">
                                 </div>
                                 <div class="col-md-3">
                                     <input type="number" wire:model="newPay.<?php echo e($loop->index); ?>.montant"
                                         class="form-control <?php $__errorArgs = ['newPay.' . $loop->index . '.montant'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                         id="example-text-input" name="address" placeholder="Entrez le montant">

                                 </div>
                                 <div class="col-md-3">
                                     <button type="button" wire:click="valider(<?php echo e($loop->index); ?>)"
                                         class="btn btn-sm btn-alt-primary" <?php if(empty($newPay[$loop->index])): echo 'disabled'; endif; ?>>Valider
                                     </button>
                                 </div>
                             </div>
                         <?php endif; ?>
                     </div>

                 </div>



             </div>
         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
     </div>
 </div>
<?php /**PATH C:\xampp\htdocs\ImsaaFoad\resources\views/livewire/caf/payment/edit.blade.php ENDPATH**/ ?>