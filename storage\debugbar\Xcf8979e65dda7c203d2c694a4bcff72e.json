{"__meta": {"id": "Xcf8979e65dda7c203d2c694a4bcff72e", "datetime": "2025-07-01 17:56:30", "utime": 1751381790.80807, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751381789.412602, "end": 1751381790.808102, "duration": 1.3954999446868896, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1751381789.412602, "relative_start": 0, "end": 1751381790.600781, "relative_end": 1751381790.600781, "duration": 1.1881790161132812, "duration_str": "1.19s", "params": [], "collector": null}, {"label": "Application", "start": 1751381790.601606, "relative_start": 1.1890039443969727, "end": 1751381790.808105, "relative_end": 3.0994415283203125e-06, "duration": 0.2064990997314453, "duration_str": "206ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 24172552, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "foadregister (\\resources\\views\\foadregister.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/foadregister.blade.php&line=0"}, {"name": "livewire.multi-step-register (\\resources\\views\\livewire\\multi-step-register.blade.php)", "param_count": 17, "params": ["errors", "_instance", "newUser", "niveau", "parcour", "parcours", "cv", "diplome", "releveb<PERSON><PERSON>", "releve1", "releve2", "releve3", "releve4", "terms", "iteration", "currentStep", "previewDocument"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/multi-step-register.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 4, "params": ["__env", "app", "errors", "html"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "\\Illuminate\\Routing\\ViewController@__invoke", "controller": "\\Illuminate\\Routing\\ViewController", "namespace": null, "prefix": "", "where": [], "as": "register"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": {"multi-step-register #iy2Y5WkfuTzzp2kdh9wt": "array:5 [\n  \"data\" => array:15 [\n    \"newUser\" => []\n    \"niveau\" => null\n    \"parcour\" => null\n    \"parcours\" => Illuminate\\Support\\Collection {#758\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"cv\" => null\n    \"diplome\" => null\n    \"relevebacc\" => null\n    \"releve1\" => null\n    \"releve2\" => null\n    \"releve3\" => null\n    \"releve4\" => null\n    \"terms\" => null\n    \"iteration\" => null\n    \"currentStep\" => \"parcours\"\n    \"previewDocument\" => null\n  ]\n  \"name\" => \"multi-step-register\"\n  \"view\" => \"livewire.multi-step-register\"\n  \"component\" => \"App\\Http\\Livewire\\MultiStepRegister\"\n  \"id\" => \"iy2Y5WkfuTzzp2kdh9wt\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fadT46xXORske6XZ9t0emo4SVvAEHPCtarr5KdaA", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1040543811 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1040543811\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1479784099 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1479784099\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1442553515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1442553515\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-862490112 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862490112\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-488122600 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58776</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751381789.4126</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751381789</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488122600\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1084511422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1084511422\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-317054038 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:56:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdGQ2x6cHNtWG1TZDkrQkJpUWNQUFE9PSIsInZhbHVlIjoiYk13SE0wYXhEdTRqRDcycXhEVWJkTngraHdBWVRpbmNleUNrcExuYmJ1YWR6QzBEd0VrWXp2M0c2bXJPM1I2d2paSUkrTVdHNGdGTlQxRXFheVRQbXJHZ0hhc2dkWU4zWUlUSjM2eFBkeXlwZFY0ZXhtY04rbGlLbGJDY012K1UiLCJtYWMiOiJhODlkOWRmMDc5M2RjY2U2OTdlMGQ4ZDUwMWM3NGFkOGZlYjYxZTc2MjNlYmNkNGU2YWY2OGFiNTMwNmNiMjg2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:56:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6ImxGVDFZTlRzRDdWRDB3VC9GaVh4MGc9PSIsInZhbHVlIjoiaU9wRmZORUttQ1VVaktqcXB0azY2WmZKOVljOFdrU1AyUEc5MWNteElEL01VVlVYM3YrcXpiU2M2M1FGNUhtMmhtejBSOE1OdEQ0VjhvMGM3L2c0VTY3VmErV0pqQXNmVmpNbDVBYjliaE96bFpybUtsTVVJNlc3dGltcXA0bXMiLCJtYWMiOiJkZThhODFmNTEyMGZjMDdlY2ZhZWMxMmRjZTBmMzBhOWEyZmViZmIwNGU1ZjhmMTk1YTI0MzY1NTgyN2IwYTlhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:56:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdGQ2x6cHNtWG1TZDkrQkJpUWNQUFE9PSIsInZhbHVlIjoiYk13SE0wYXhEdTRqRDcycXhEVWJkTngraHdBWVRpbmNleUNrcExuYmJ1YWR6QzBEd0VrWXp2M0c2bXJPM1I2d2paSUkrTVdHNGdGTlQxRXFheVRQbXJHZ0hhc2dkWU4zWUlUSjM2eFBkeXlwZFY0ZXhtY04rbGlLbGJDY012K1UiLCJtYWMiOiJhODlkOWRmMDc5M2RjY2U2OTdlMGQ4ZDUwMWM3NGFkOGZlYjYxZTc2MjNlYmNkNGU2YWY2OGFiNTMwNmNiMjg2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:56:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6ImxGVDFZTlRzRDdWRDB3VC9GaVh4MGc9PSIsInZhbHVlIjoiaU9wRmZORUttQ1VVaktqcXB0azY2WmZKOVljOFdrU1AyUEc5MWNteElEL01VVlVYM3YrcXpiU2M2M1FGNUhtMmhtejBSOE1OdEQ0VjhvMGM3L2c0VTY3VmErV0pqQXNmVmpNbDVBYjliaE96bFpybUtsTVVJNlc3dGltcXA0bXMiLCJtYWMiOiJkZThhODFmNTEyMGZjMDdlY2ZhZWMxMmRjZTBmMzBhOWEyZmViZmIwNGU1ZjhmMTk1YTI0MzY1NTgyN2IwYTlhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:56:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317054038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-331675308 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fadT46xXORske6XZ9t0emo4SVvAEHPCtarr5KdaA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331675308\", {\"maxDepth\":0})</script>\n"}}