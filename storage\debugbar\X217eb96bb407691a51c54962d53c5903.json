{"__meta": {"id": "X217eb96bb407691a51c54962d53c5903", "datetime": "2025-07-01 18:10:02", "utime": 1751382602.59406, "method": "GET", "uri": "/livewire/preview-file/mXt39SCc7Wt8EnmPMyz0X2NKWZl29I-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384401&signature=5e080c511e63fbce614425ec42bde0921dd64abce67eeda5884d8d8d3e5d1ec0", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751382602.051617, "end": 1751382602.594086, "duration": 0.5424690246582031, "duration_str": "542ms", "measures": [{"label": "Booting", "start": 1751382602.051617, "relative_start": 0, "end": 1751382602.520083, "relative_end": 1751382602.520083, "duration": 0.46846604347229004, "duration_str": "468ms", "params": [], "collector": null}, {"label": "Application", "start": 1751382602.520907, "relative_start": 0.46929001808166504, "end": 1751382602.594088, "relative_end": 2.1457672119140625e-06, "duration": 0.07318115234375, "duration_str": "73.18ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 24329392, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/preview-file/{filename}", "controller": "Livewire\\Controllers\\FilePreviewHandler@handle", "as": "livewire.preview-file", "middleware": "web", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\FilePreviewHandler.php&line=11\">\\vendor\\livewire\\livewire\\src\\Controllers\\FilePreviewHandler.php:11-19</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/livewire/preview-file/mXt39SCc7Wt8EnmPMyz0X2NKWZl29I-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384401&signature=5e080c511e63fbce614425ec42bde0921dd64abce67eeda5884d8d8d3e5d1ec0\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:16 [\n    \"sexe\" => \"F\"\n    \"date_naissance\" => \"1995-08-07\"\n    \"lieu_naissance\" => \"In rerum recusandae\"\n    \"adresse\" => \"Voluptates ut tempor\"\n    \"nationalite\" => \"Sed aut odio volupta\"\n    \"telephone2\" => \"+18795584611\"\n    \"cin\" => \"123412341234\"\n    \"date_delivrance\" => \"2020-01-24\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"nom\" => \"Aphrodite\"\n    \"prenom\" => \"Macdonald\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261 32 55 516 50\"\n    \"reference\" => \"21324\"\n    \"telenvoi\" => \"123412\"\n    \"montantenvoi\" => \"12341234\"\n  ]\n  \"niveau\" => \"2\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n]"}, "request": {"path_info": "/livewire/preview-file/mXt39SCc7Wt8EnmPMyz0X2NKWZl29I-metaYWxpcmVuY2lhLnBuZw==-.png", "status_code": "<pre class=sf-dump id=sf-dump-1411658916 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1411658916\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-765916693 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1751384401</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">5e080c511e63fbce614425ec42bde0921dd64abce67eeda5884d8d8d3e5d1ec0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765916693\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-586837689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-586837689\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-184885416 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImJscGNxU25WUG9uVlVqaGpHQXBQa0E9PSIsInZhbHVlIjoiUmgzTjRVVy9aa3JkSHlWRGJZa3B6YnBtZm9IakJSMTNxYmFRS3I1VFE3Q1dPYitXcGpmSEhreGtvN25IZ0hTVXEwNVh6ZzJRQ2VxQ0dVa2lmNndKbnJ1djBaaDk3b25sMWhjeXNreTJCSHVscE5tQW5TM1lrVmxSZDF0Q2R0K1YiLCJtYWMiOiIzNjNlY2M4Y2M4YzExNzZjZjIwNzcxOTc1YTkxN2QyNDhiMzUwNjNkNTc4NDY4ZGYxZDEyNGQwNjc0MmM0YjNkIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkRUR3o4dlRIRjlQcEd5SmJPTkJXRVE9PSIsInZhbHVlIjoiN0JzMnV0eFNlNUpocDBPNFIxUUp4eXNyZ3Y0QVBNcVRjREk3NEpERHpUMDB3bWtZanhNNG9hVmxpUVo4RkwyTE5Xdy83UUYzbjhZZ3FWRmpBYWZHYUxDVDJmQmxYMjVxUXhYdFBxRENZR1dUL0t2cnY1WUdBVnZ4V3RSNnNVbm4iLCJtYWMiOiJmMGRiYTU5ZTk5YzM2ZGY4ZmFhZTZkYmNhZTAwNzM3YzE2NDVjOWFiY2M0OTY5MTU4ODVhODMxNzcxYTQ3MDQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184885416\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1025404429 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59665</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"177 characters\">/livewire/preview-file/mXt39SCc7Wt8EnmPMyz0X2NKWZl29I-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384401&amp;signature=5e080c511e63fbce614425ec42bde0921dd64abce67eeda5884d8d8d3e5d1ec0</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"83 characters\">/livewire/preview-file/mXt39SCc7Wt8EnmPMyz0X2NKWZl29I-metaYWxpcmVuY2lhLnBuZw==-.png</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"108 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console/../resources/server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"83 characters\">/livewire/preview-file/mXt39SCc7Wt8EnmPMyz0X2NKWZl29I-metaYWxpcmVuY2lhLnBuZw==-.png</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"93 characters\">expires=1751384401&amp;signature=5e080c511e63fbce614425ec42bde0921dd64abce67eeda5884d8d8d3e5d1ec0</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImJscGNxU25WUG9uVlVqaGpHQXBQa0E9PSIsInZhbHVlIjoiUmgzTjRVVy9aa3JkSHlWRGJZa3B6YnBtZm9IakJSMTNxYmFRS3I1VFE3Q1dPYitXcGpmSEhreGtvN25IZ0hTVXEwNVh6ZzJRQ2VxQ0dVa2lmNndKbnJ1djBaaDk3b25sMWhjeXNreTJCSHVscE5tQW5TM1lrVmxSZDF0Q2R0K1YiLCJtYWMiOiIzNjNlY2M4Y2M4YzExNzZjZjIwNzcxOTc1YTkxN2QyNDhiMzUwNjNkNTc4NDY4ZGYxZDEyNGQwNjc0MmM0YjNkIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkRUR3o4dlRIRjlQcEd5SmJPTkJXRVE9PSIsInZhbHVlIjoiN0JzMnV0eFNlNUpocDBPNFIxUUp4eXNyZ3Y0QVBNcVRjREk3NEpERHpUMDB3bWtZanhNNG9hVmxpUVo4RkwyTE5Xdy83UUYzbjhZZ3FWRmpBYWZHYUxDVDJmQmxYMjVxUXhYdFBxRENZR1dUL0t2cnY1WUdBVnZ4V3RSNnNVbm4iLCJtYWMiOiJmMGRiYTU5ZTk5YzM2ZGY4ZmFhZTZkYmNhZTAwNzM3YzE2NDVjOWFiY2M0OTY5MTU4ODVhODMxNzcxYTQ3MDQ2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751382602.0516</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751382602</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025404429\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2115457305 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hRF2mVuiU5BibRd5o4CekB6Fo0oqlULGaPW0Otn7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115457305\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1441218136 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">image/png; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 15:10:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:09:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:10:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">4171891</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IitiU1JXajlSMy93dkZidU51WlpZQWc9PSIsInZhbHVlIjoibTF6OEhCUzBmb1NEQ0xxZi9zOXhiWitSVWJ0TWpKbTB3VjZUUnYrcHl3MFcrdDJwUWFMVzlJMmNzdWtmYW1wZ2sySU5WUGJQVFFsSDhRS3pWQkZNek80SXVEazhSV2xTSkZXSWpMakJRTFZjT2YyTHdCZzRhQW5meTcvQzkzS0EiLCJtYWMiOiJlODI4ZDdjNzhiZWEwNDRkMDc4ZmNiMjY5OTY5YTIyNzQ3M2Y2Y2VmMTgwNGFhYjkxM2RjY2E5Njc3MDJiN2ZjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:10:02 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6InUxb0w4TjVNa0ZMUk5GZjhRbjFQenc9PSIsInZhbHVlIjoiYmVoVDRHYmZHV3RqVEc0czZPVElrYldLdnNXbDNTdUROK28zZXltQ0phcm9XbTlTTEJZU0ZzL0dDK0VCdzhydHFuZ05DMlhpbnZLaGRiVFZpYXQwbjRicTZUWmdDdlZsN1BiK0hXb1IvUWIyWm4vOWlDaVMwbUxpc0dUWFZZci8iLCJtYWMiOiJlYmY1MTI0ZTk4MGNkNmQ4N2U4YWNhMTI5NDlkNzdkMGFkYWVkYzQzYWM3MTAwM2IzYzFhMTAxYjI5MmQ1ZjFkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:10:02 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IitiU1JXajlSMy93dkZidU51WlpZQWc9PSIsInZhbHVlIjoibTF6OEhCUzBmb1NEQ0xxZi9zOXhiWitSVWJ0TWpKbTB3VjZUUnYrcHl3MFcrdDJwUWFMVzlJMmNzdWtmYW1wZ2sySU5WUGJQVFFsSDhRS3pWQkZNek80SXVEazhSV2xTSkZXSWpMakJRTFZjT2YyTHdCZzRhQW5meTcvQzkzS0EiLCJtYWMiOiJlODI4ZDdjNzhiZWEwNDRkMDc4ZmNiMjY5OTY5YTIyNzQ3M2Y2Y2VmMTgwNGFhYjkxM2RjY2E5Njc3MDJiN2ZjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:10:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6InUxb0w4TjVNa0ZMUk5GZjhRbjFQenc9PSIsInZhbHVlIjoiYmVoVDRHYmZHV3RqVEc0czZPVElrYldLdnNXbDNTdUROK28zZXltQ0phcm9XbTlTTEJZU0ZzL0dDK0VCdzhydHFuZ05DMlhpbnZLaGRiVFZpYXQwbjRicTZUWmdDdlZsN1BiK0hXb1IvUWIyWm4vOWlDaVMwbUxpc0dUWFZZci8iLCJtYWMiOiJlYmY1MTI0ZTk4MGNkNmQ4N2U4YWNhMTI5NDlkNzdkMGFkYWVkYzQzYWM3MTAwM2IzYzFhMTAxYjI5MmQ1ZjFkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:10:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441218136\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1421717326 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fSz3B1e421pYinvnbkCTDdA5IySBwZsj4AbH65dX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"198 characters\">http://localhost:8000/livewire/preview-file/mXt39SCc7Wt8EnmPMyz0X2NKWZl29I-metaYWxpcmVuY2lhLnBuZw==-.png?expires=1751384401&amp;signature=5e080c511e63fbce614425ec42bde0921dd64abce67eeda5884d8d8d3e5d1ec0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:16</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1995-08-07</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"19 characters\">In rerum recusandae</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Voluptates ut tempor</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed aut odio volupta</span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+18795584611</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2020-01-24</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Aphrodite</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Macdonald</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"19 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n      \"<span class=sf-dump-key>reference</span>\" => \"<span class=sf-dump-str title=\"5 characters\">21324</span>\"\n      \"<span class=sf-dump-key>telenvoi</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123412</span>\"\n      \"<span class=sf-dump-key>montantenvoi</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12341234</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>2</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421717326\", {\"maxDepth\":0})</script>\n"}}