<form method="POST" wire:submit.prevent="register()" enctype="multipart/form-data">
    <!-- Auto-save Indicator -->
    <div class="auto-save-indicator" style="display: none;">
        <i class="fa fa-save me-1"></i>
        <span class="indicator-text">Sauvegardé</span>
    </div>

    <div class="block block-rounded">
        <div class="block-content">
            <!-- Enhanced Progress Bar -->
            <div class="progress-steps mb-5">
                <!-- Progress Line -->
                <div class="progress-line-container">
                    <div class="progress-line" style="width: <?php echo e($this->getProgressPercentage()); ?>%;"></div>
                </div>

                <!-- Steps -->
                <div class="steps-container">
                    <?php
                        $steps = [
                            'parcours' => ['icon' => 'fa-graduation-cap', 'label' => 'Parcours', 'description' => 'Choisir votre formation'],
                            'informations' => ['icon' => 'fa-user', 'label' => 'Informations', 'description' => 'Vos données personnelles'],
                            'documents' => ['icon' => 'fa-file-alt', 'label' => 'Documents', 'description' => 'Pièces justificatives'],
                            'paiement' => ['icon' => 'fa-credit-card', 'label' => 'Paiement', 'description' => 'Finaliser l\'inscription']
                        ];
                    ?>

                    <?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stepKey => $stepData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $isCompleted = $this->isStepComplete($stepKey);
                            $isCurrent = $currentStep === $stepKey;
                            $canAccess = $this->canAccessStep($stepKey);
                        ?>

                        <div class="step <?php echo e($isCompleted ? 'completed' : ''); ?> <?php echo e($isCurrent ? 'current' : ''); ?> <?php echo e(!$canAccess ? 'disabled' : ''); ?>"
                             <?php if($canAccess): ?> wire:click="goToStep('<?php echo e($stepKey); ?>')" <?php endif; ?>
                             style="cursor: <?php echo e($canAccess ? 'pointer' : 'not-allowed'); ?>;">

                            <div class="step-icon-wrapper">
                                <div class="step-icon">
                                    <?php if($isCompleted): ?>
                                        <i class="fa fa-check"></i>
                                    <?php elseif($isCurrent): ?>
                                        <i class="fa <?php echo e($stepData['icon']); ?> fa-pulse"></i>
                                    <?php else: ?>
                                        <i class="fa <?php echo e($stepData['icon']); ?>"></i>
                                    <?php endif; ?>
                                </div>
                                <?php if($isCurrent): ?>
                                    <div class="step-pulse"></div>
                                <?php endif; ?>
                            </div>

                            <div class="step-content">
                                <div class="step-label"><?php echo e($stepData['label']); ?></div>
                                <div class="step-description"><?php echo e($stepData['description']); ?></div>
                            </div>

                            <?php if($isCompleted): ?>
                                <div class="step-checkmark">
                                    <i class="fa fa-check-circle"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Progress Info -->
                <div class="progress-info mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="progress-text">
                            <small class="text-muted">
                                Étape <?php echo e(array_search($currentStep, array_keys($steps)) + 1); ?> sur <?php echo e(count($steps)); ?>

                            </small>
                        </div>
                        <div class="progress-percentage">
                            <span class="badge bg-primary"><?php echo e(round($this->getProgressPercentage())); ?>%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Navigation buttons -->
            <div class="navigation-container mt-4 mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <!-- Previous Button -->
                    <div class="nav-button-wrapper">
                        <?php if($currentStep !== 'parcours'): ?>
                            <button type="button" class="btn btn-outline-secondary btn-nav" wire:click="previousStep" wire:loading.attr="disabled" wire:target="previousStep">
                                <span wire:loading.remove wire:target="previousStep">
                                    <i class="fa fa-arrow-left me-2"></i>
                                    <span class="btn-text">
                                        <span class="btn-main">Précédent</span>
                                        <small class="btn-sub d-block">
                                            <?php switch($currentStep):
                                                case ('informations'): ?> Retour au parcours <?php break; ?>
                                                <?php case ('documents'): ?> Retour aux informations <?php break; ?>
                                                <?php case ('paiement'): ?> Retour aux documents <?php break; ?>
                                            <?php endswitch; ?>
                                        </small>
                                    </span>
                                </span>
                                <span wire:loading wire:target="previousStep" class="d-flex align-items-center" style="display: none;">
                                    <i class="fa fa-spinner fa-spin me-2"></i> Chargement...
                                </span>
                            </button>
                        <?php else: ?>
                            <div></div>
                        <?php endif; ?>
                    </div>

                    <!-- Step Validation Indicator -->
                    <div class="step-validation text-center">
                        <?php if($this->isStepComplete($currentStep)): ?>
                            <div class="validation-success">
                                <i class="fa fa-check-circle text-success me-1"></i>
                                <small class="text-success fw-bold">Étape complète</small>
                            </div>
                        <?php else: ?>
                            <div class="validation-pending">
                                <i class="fa fa-clock text-warning me-1"></i>
                                <small class="text-muted">Complétez cette étape</small>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Next/Submit Button -->
                    <div class="nav-button-wrapper">
                        <?php if($currentStep === 'paiement'): ?>
                            <button type="button" class="btn btn-success btn-nav btn-submit" wire:click="register" wire:loading.attr="disabled" wire:target="register" <?php echo e(!$this->canProceedToNextStep() || !$terms ? 'disabled' : ''); ?>>
                                <span wire:loading.remove wire:target="register">
                                    <span class="btn-text">
                                        <span class="btn-main">
                                            <i class="fa fa-paper-plane me-2"></i>Finaliser l'inscription
                                        </span>
                                        <small class="btn-sub d-block">Soumettre votre dossier</small>
                                    </span>
                                </span>
                                <span wire:loading wire:target="register" class="d-flex align-items-center" style="display: none;">
                                    <i class="fa fa-spinner fa-spin me-2"></i> Inscription en cours...
                                </span>
                            </button>
                        <?php else: ?>
                            <button type="button" class="btn btn-primary btn-nav" wire:click="nextStep" <?php echo e(!$this->canProceedToNextStep() ? 'disabled' : ''); ?> wire:loading.attr="disabled" wire:target="nextStep">
                                <span wire:loading.remove wire:target="nextStep">
                                    <span class="btn-text">
                                        <span class="btn-main">
                                            Suivant
                                            <i class="fa fa-arrow-right ms-2"></i>
                                        </span>
                                        <small class="btn-sub d-block">
                                            <?php switch($currentStep):
                                                case ('parcours'): ?> Vos informations <?php break; ?>
                                                <?php case ('informations'): ?> Vos documents <?php break; ?>
                                                <?php case ('documents'): ?> Paiement <?php break; ?>
                                            <?php endswitch; ?>
                                        </small>
                                    </span>
                                </span>
                                <span wire:loading wire:target="nextStep" class="d-flex align-items-center" style="display: none;">
                                    <i class="fa fa-spinner fa-spin me-2"></i> Chargement...
                                </span>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Progress Summary -->
                <div class="progress-summary mt-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="summary-item">
                                <div class="summary-number"><?php echo e(array_search($currentStep, ['parcours', 'informations', 'documents', 'paiement']) + 1); ?></div>
                                <div class="summary-label">Étape actuelle</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="summary-item">
                                <div class="summary-number"><?php echo e(collect(['parcours', 'informations', 'documents', 'paiement'])->filter(fn($step) => $this->isStepComplete($step))->count()); ?></div>
                                <div class="summary-label">Étapes complètes</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="summary-item">
                                <div class="summary-number"><?php echo e(4 - collect(['parcours', 'informations', 'documents', 'paiement'])->filter(fn($step) => $this->isStepComplete($step))->count()); ?></div>
                                <div class="summary-label">Restantes</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php $__errorArgs = ['step'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-circle me-1"></i>
                    <?php echo e($message); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

            <!-- Form Content -->
            <div class="form-content">
                <?php if($currentStep === 'parcours'): ?>
                    <h2 class="content-heading text-center border-bottom my-4 pb-2">Choix Parcours</h2>
    <div class="row g-4 mb-4">
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-select">Niveau<span class="text-danger">*</span></label>
            <select class="form-select form-control-alt <?php $__errorArgs = ['niveau'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model="niveau"
                id="example-select" name="example-select">
                <option selected value="">-- Choisir Niveau --</option>
                <option value="1">1ère année</option>
                <option value="2">2ème année (DTS)</option>
                <option value="3">3ème année (Licence)</option>
                <option value="4">4ème année (Master I)</option>
                <option value="5">5ème année (Master II)</option>
            </select>

            <?php $__errorArgs = ['niveau'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-select1">Mention<span class="text-danger">*</span></label>
            <select class="form-select form-control-alt <?php $__errorArgs = ['parcour'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model="parcour"
                id="example-select1" name="example-select1">
                <?php if($parcours->count() == 0): ?>
                    <option selected value="">-- Veuillez d'abord choisir le niveau --</option>
                <?php endif; ?>

                <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->nom); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>

            <?php $__errorArgs = ['parcour'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>
                <?php elseif($currentStep === 'informations'): ?>
    <h2 class="content-heading text-center border-bottom my-4 pb-2">Renseignement sur l'étudiant</h2>
    <div class="row g-4">
        <div class="col-md-12 col-lg-6">
            <div class="form-group-enhanced">
                <label class="form-label" for="input-nom">
                    <i class="fa fa-user me-1"></i>Nom <span class="text-danger">*</span>
                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Veuillez entrer votre nom complet"></i>
                </label>
                <div class="input-wrapper">
                    <input type="text" wire:model.debounce.300ms="newUser.nom"
                        class="form-control form-control-enhanced <?php $__errorArgs = ['newUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> <?php if(!empty($newUser['nom']) && !$errors->has('newUser.nom')): ?> is-valid <?php endif; ?>"
                        id="input-nom" name="firstname" placeholder="Nom de l'étudiant">
                    <div class="input-feedback">
                        <?php if(!empty($newUser['nom']) && !$errors->has('newUser.nom')): ?>
                            <i class="fa fa-check-circle text-success"></i>
                        <?php elseif($errors->has('newUser.nom')): ?>
                            <i class="fa fa-exclamation-circle text-danger"></i>
                        <?php endif; ?>
                    </div>
                </div>
                <?php $__errorArgs = ['newUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback d-block">
                        <i class="fa fa-exclamation-triangle me-1"></i><?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <?php if(!empty($newUser['nom']) && !$errors->has('newUser.nom')): ?>
                    <div class="valid-feedback d-block">
                        <i class="fa fa-check me-1"></i>Nom valide
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-md-12 col-lg-6">
            <div class="form-group-enhanced">
                <label class="form-label" for="input-prenom">
                    <i class="fa fa-user me-1"></i>Prénom <span class="text-danger">*</span>
                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Veuillez entrer votre prénom"></i>
                </label>
                <div class="input-wrapper">
                    <input type="text" wire:model.debounce.300ms="newUser.prenom"
                        class="form-control form-control-enhanced <?php $__errorArgs = ['newUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> <?php if(!empty($newUser['prenom']) && !$errors->has('newUser.prenom')): ?> is-valid <?php endif; ?>"
                        id="input-prenom" name="lastname" placeholder="Prénom de l'étudiant">
                    <div class="input-feedback">
                        <?php if(!empty($newUser['prenom']) && !$errors->has('newUser.prenom')): ?>
                            <i class="fa fa-check-circle text-success"></i>
                        <?php elseif($errors->has('newUser.prenom')): ?>
                            <i class="fa fa-exclamation-circle text-danger"></i>
                        <?php endif; ?>
                    </div>
                </div>
                <?php $__errorArgs = ['newUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback d-block">
                        <i class="fa fa-exclamation-triangle me-1"></i><?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <?php if(!empty($newUser['prenom']) && !$errors->has('newUser.prenom')): ?>
                    <div class="valid-feedback d-block">
                        <i class="fa fa-check me-1"></i>Prénom valide
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-date-naissance">Date de naissance <span
                    class="text-danger">*</span></label>
            <input type="date" wire:model="newUser.date_naissance" 
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="input-date-naissance" name="date_naissance" data-week-start="1" data-autoclose="true"
                data-today-highlight="true" data-date-format="dd/mm/yyyy" placeholder="jj/mm/aaaa">
            <?php $__errorArgs = ['newUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-sexe">Sexe <span class="text-danger">*</span></label>
            <select class="form-select form-control-alt <?php $__errorArgs = ['newUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                wire:model="newUser.sexe" id="input-sexe" name="example-select">
                <option selected value="">-- Choisir sexe --</option>
                <option value="H">Homme</option>
                <option value="F">Femme</option>
            </select>
            <?php $__errorArgs = ['newUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-lieu-naissance">Lieu de naissance <span
                    class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.lieu_naissance"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="input-lieu-naissance" name="example-text-input" placeholder="Lieu de naissance">
            <?php $__errorArgs = ['newUser.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-adresse">Adresse <span class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.adresse"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.adresse'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="input-adresse" name="address" placeholder="Adresse actuel">
            <?php $__errorArgs = ['newUser.adresse'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="input-nationalite">Nationalité <span class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.nationalite"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.nationalite'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="input-nationalite" name="example-text-input" placeholder="Nationalité">
            <?php $__errorArgs = ['newUser.nationalite'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        

        <div class="col-md-12 col-lg-6">
            <div class="form-group-enhanced">
                <label class="form-label" for="example-email-input">
                    <i class="fa fa-envelope me-1"></i>Email <span class="text-danger">*</span>
                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Veuillez entrer une adresse email valide"></i>
                </label>
                <div class="input-wrapper">
                    <div class="input-group">
                        <input type="email" wire:model.debounce.500ms="newUser.email"
                            class="form-control form-control-enhanced <?php $__errorArgs = ['newUser.email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> <?php if(!empty($newUser['email']) && !$errors->has('newUser.email') && filter_var($newUser['email'], FILTER_VALIDATE_EMAIL)): ?> is-valid <?php endif; ?>"
                            id="example-email-input" name="example-email-input" placeholder="<EMAIL>">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-at"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">Suggestions d'email</h6></li>
                            <?php if(!empty($newUser['nom']) && !empty($newUser['prenom'])): ?>
                                <li><a class="dropdown-item" href="#" wire:click.prevent="$set('newUser.email', '<?php echo e(strtolower($newUser['nom'] ?? '')); ?>.<?php echo e(strtolower($newUser['prenom'] ?? '')); ?>@gmail.com')">
                                    <i class="fa fa-envelope me-2"></i><?php echo e(strtolower($newUser['nom'] ?? '')); ?>.<?php echo e(strtolower($newUser['prenom'] ?? '')); ?>@gmail.com
                                </a></li>
                                <li><a class="dropdown-item" href="#" wire:click.prevent="$set('newUser.email', '<?php echo e(strtolower($newUser['nom'] ?? '')); ?>.<?php echo e(strtolower($newUser['prenom'] ?? '')); ?>@yahoo.com')">
                                    <i class="fa fa-envelope me-2"></i><?php echo e(strtolower($newUser['nom'] ?? '')); ?>.<?php echo e(strtolower($newUser['prenom'] ?? '')); ?>@yahoo.com
                                </a></li>
                                <li><a class="dropdown-item" href="#" wire:click.prevent="$set('newUser.email', '<?php echo e(strtolower($newUser['nom'] ?? '')); ?>.<?php echo e(strtolower($newUser['prenom'] ?? '')); ?>@hotmail.com')">
                                    <i class="fa fa-envelope me-2"></i><?php echo e(strtolower($newUser['nom'] ?? '')); ?>.<?php echo e(strtolower($newUser['prenom'] ?? '')); ?>@hotmail.com
                                </a></li>
                            <?php else: ?>
                                <li><span class="dropdown-item-text text-muted">Remplissez d'abord nom et prénom</span></li>
                            <?php endif; ?>
                        </ul>
                        <div class="input-feedback">
                            <?php if(!empty($newUser['email']) && !$errors->has('newUser.email') && filter_var($newUser['email'], FILTER_VALIDATE_EMAIL)): ?>
                                <i class="fa fa-check-circle text-success"></i>
                            <?php elseif($errors->has('newUser.email')): ?>
                                <i class="fa fa-exclamation-circle text-danger"></i>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="form-feedback">
                    <div class="form-text" wire:loading wire:target="newUser.email">
                        <i class="fa fa-spinner fa-spin me-1"></i> Vérification de l'email...
                    </div>
                    <?php $__errorArgs = ['newUser.email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback d-block">
                            <i class="fa fa-exclamation-triangle me-1"></i><?php echo e($message); ?>

                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <?php if(!empty($newUser['email']) && !$errors->has('newUser.email') && filter_var($newUser['email'], FILTER_VALIDATE_EMAIL)): ?>
                        <div class="valid-feedback d-block">
                            <i class="fa fa-check me-1"></i>Email valide
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-12 col-lg-6">
            <div class="form-group-enhanced">
                <label class="form-label" for="telephone1-input">
                    <i class="fa fa-phone me-1"></i>Téléphone <span class="text-danger">*</span>
                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Format: +261 XX XX XXX XX"></i>
                </label>
                <div class="input-wrapper">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fa fa-phone text-muted"></i>
                        </span>
                        <input type="text" wire:model.debounce.500ms="newUser.telephone1"
                            class="form-control form-control-enhanced <?php $__errorArgs = ['newUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> <?php if(!empty($newUser['telephone1']) && !$errors->has('newUser.telephone1') && preg_match('/^\+261\s?\d{2}\s?\d{2}\s?\d{3}\s?\d{2}$/', $newUser['telephone1'])): ?> is-valid <?php endif; ?>"
                            id="telephone1-input" name="telephone1" placeholder="+261 XX XX XXX XX"
                            maxlength="17">
                        <div class="input-feedback">
                            <?php if(!empty($newUser['telephone1']) && !$errors->has('newUser.telephone1') && preg_match('/^\+261\s?\d{2}\s?\d{2}\s?\d{3}\s?\d{2}$/', $newUser['telephone1'])): ?>
                                <i class="fa fa-check-circle text-success"></i>
                            <?php elseif($errors->has('newUser.telephone1')): ?>
                                <i class="fa fa-exclamation-circle text-danger"></i>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="form-feedback">
                    <div class="form-text">
                        <small class="text-muted">
                            <i class="fa fa-info-circle me-1"></i>
                            Format attendu : +261 XX XX XXX XX (numéro malgache)
                        </small>
                    </div>
                    <?php $__errorArgs = ['newUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback d-block">
                            <i class="fa fa-exclamation-triangle me-1"></i><?php echo e($message); ?>

                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <?php if(!empty($newUser['telephone1']) && !$errors->has('newUser.telephone1') && preg_match('/^\+261\s?\d{2}\s?\d{2}\s?\d{3}\s?\d{2}$/', $newUser['telephone1'])): ?>
                        <div class="valid-feedback d-block">
                            <i class="fa fa-check me-1"></i>Numéro de téléphone valide
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Telephone (2)</label>
            <input type="text" wire:model="newUser.telephone2" class="form-control form-control-alt"
                id="example-text-input" name="example-text-input" placeholder="Numéro téléphone (Facultatif)">
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">CIN</label>
            <input type="text" wire:model="newUser.cin"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.cin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="example-text-input" name="example-text-input"
                placeholder="Numéro du CIN (Laissez vide si vous êtes mineur)">

            <?php $__errorArgs = ['newUser.cin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Date de délivrance </label>
            <input type="date" wire:model="newUser.date_delivrance"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.date_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="date_delivrance" name="date_delivrance" data-week-start="1" data-autoclose="true"
                data-today-highlight="true" data-date-format="dd/mm/yyyy" placeholder="jj/mm/aaaa">

            <?php $__errorArgs = ['newUser.date_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Lieu de délivrance</label>
            <input type="text" wire:model="newUser.lieu_delivrance"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.lieu_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="example-text-input" name="example-text-input" placeholder="Lieu de délivrance CIN">

            <?php $__errorArgs = ['newUser.lieu_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>
                <?php elseif($currentStep === 'documents'): ?>
    <h2 class="content-heading text-center border-bottom my-4 pb-2">Pièces jointes à fournir</h2>
    <?php if($niveau == 1): ?>
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                <div class="file-upload-enhanced">
                    <label class="form-label">
                        <i class="fa fa-file-pdf me-1"></i>Relevé ou Diplôme BACC
                        <span class="text-danger">*</span>
                        <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                    </label>

                    <?php if(!$relevebacc): ?>
                        <div class="file-drop-zone" onclick="document.getElementById('upload1<?php echo e($iteration); ?>').click()">
                            <div class="drop-zone-content">
                                <div class="drop-zone-icon">
                                    <i class="fa fa-cloud-upload-alt fa-3x text-primary"></i>
                                </div>
                                <div class="drop-zone-text">
                                    <h6 class="mb-1 text-dark">Cliquez pour sélectionner votre fichier</h6>
                                    <p class="text-muted mb-2">ou glissez-déposez ici</p>
                                    <small class="text-muted">PDF, JPG, PNG (Max 5MB)</small>
                                </div>
                            </div>

                            <input type="file"
                                   wire:model="relevebacc"
                                   class="file-input <?php $__errorArgs = ['relevebacc'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="upload1<?php echo e($iteration); ?>"
                                   accept=".pdf,.jpg,.jpeg,.png"
                                   style="display: none;">
                        </div>
                    <?php else: ?>
                        <div class="file-preview">
                            <div class="file-preview-header">
                                <div class="file-info">
                                    <?php if(in_array($relevebacc->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                        <div class="file-thumbnail">
                                            <img src="<?php echo e($relevebacc->temporaryUrl()); ?>" alt="Aperçu">
                                        </div>
                                    <?php else: ?>
                                        <div class="file-icon">
                                            <i class="fa fa-file-pdf text-danger"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="file-details">
                                        <div class="file-name"><?php echo e($relevebacc->getClientOriginalName()); ?></div>
                                        <div class="file-size"><?php echo e(number_format($relevebacc->getSize() / 1024, 1)); ?> KB</div>
                                    </div>
                                </div>
                                <div class="file-actions">
                                    <button type="button" class="btn btn-sm btn-outline-primary" wire:click="previewDocument('relevebacc')">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" wire:click="removeFile('relevebacc')">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="upload-progress">
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: 100%"></div>
                                </div>
                                <small class="text-success">
                                    <i class="fa fa-check me-1"></i>Fichier téléchargé avec succès
                                </small>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div wire:loading wire:target="relevebacc" class="upload-loading" style="display: none;">
                        <div class="loading-content">
                            <div class="spinner-border spinner-border-sm text-primary me-2"></div>
                            <span>Téléchargement en cours...</span>
                        </div>
                        <div class="progress mt-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                        </div>
                    </div>

                    <?php $__errorArgs = ['relevebacc'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback d-block mt-2">
                            <i class="fa fa-exclamation-triangle me-1"></i><?php echo e($message); ?>

                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>
    <?php elseif($niveau == 2): ?>
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload2<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                <?php if($cv): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($cv->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($cv->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme BACC
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload3<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($diplome): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($diplome->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($diplome->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Relevé L1
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve1"
                                    class="form-control <?php $__errorArgs = ['releve1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload4<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve1" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($releve1): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($releve1->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($releve1->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($releve1->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve1')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['releve1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    <?php elseif($niveau == 3): ?>
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload5<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                <?php if($cv): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($cv->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($cv->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme BACC
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload6<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($diplome): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($diplome->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($diplome->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Relevé L2
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve2"
                                    class="form-control <?php $__errorArgs = ['releve2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload7<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve2" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($releve2): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($releve2->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($releve2->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($releve2->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve2')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['releve2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    <?php elseif($niveau == 4): ?>
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload8<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                <?php if($cv): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($cv->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($cv->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme de DTS ou Licence
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload9<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($diplome): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($diplome->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($diplome->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-8">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Attestation de travail (2 ans d'expérience)
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve3"
                                    class="form-control <?php $__errorArgs = ['releve3'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload10<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve3" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($releve3): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($releve3->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($releve3->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($releve3->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve3')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['releve3'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    <?php elseif($niveau == 5): ?>
        <div class="row g-4">
            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>CV
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                                <input type="file" wire:model="cv"
                                    class="form-control <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload11<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    name="example-text-input">

                                <div wire:loading wire:target="cv" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                </div>

                                <?php if($cv): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($cv->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($cv->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($cv->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('cv')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['cv'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Diplôme de Licence
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="diplome"
                                    class="form-control <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload12<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="diplome" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($diplome): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($diplome->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($diplome->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($diplome->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('diplome')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['diplome'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-12 col-lg-6">
                                <label class="form-label" for="example-text-input">
                                    <i class="fa fa-file-pdf me-1"></i>Relevé M1
                                    <i class="fa fa-info-circle ms-1" data-bs-toggle="tooltip" title="Formats acceptés: PDF, JPG, PNG (Max 5MB)"></i>
                                </label>
                <input type="file" wire:model="releve4"
                                    class="form-control <?php $__errorArgs = ['releve4'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="upload13<?php echo e($iteration); ?>"
                                    accept=".pdf,.jpg,.jpeg,.png"
                    name="example-text-input">

                                <div wire:loading wire:target="releve4" class="text-primary mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Téléchargement en cours...
                                </div>

                                <?php if($releve4): ?>
                                    <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(in_array($releve4->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                                                <div class="me-2">
                                                    <img src="<?php echo e($releve4->temporaryUrl()); ?>" 
                                                         class="img-thumbnail" 
                                                         style="max-height: 50px; max-width: 50px;"
                                                         alt="Aperçu">
                                                </div>
                                            <?php else: ?>
                                                <i class="fa fa-file-pdf text-danger me-2"></i>
                                            <?php endif; ?>
                                            <span class="text-truncate"><?php echo e($releve4->getClientOriginalName()); ?></span>
                                            <button type="button" class="btn btn-sm btn-alt-danger ms-2" wire:click="removeFile('releve4')">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                <?php $__errorArgs = ['releve4'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <i class="fa fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-warning d-flex align-items-center justify-content-between" role="alert">
            <div class="flex-grow-1 me-3">
                <p class="mb-0">
                    Choisissez d'abord votre niveau
                </p>
            </div>
            <div class="flex-shrink-0">
                <i class="fa fa-fw fa-exclamation-circle"></i>
            </div>
        </div>
    <?php endif; ?>
                <?php elseif($currentStep === 'paiement'): ?>
                    <h2 class="content-heading text-center border-bottom my-4 pb-2">Suivi de paiement</h2>
    <div class="alert alert-info d-flex align-items-center" role="alert">
        <div class="flex-shrink-0">
            <i class="fa fa-fw fa-info-circle"></i>
        </div>
        <div class="flex-grow-1 ms-3">
            <p class="mb-0">
                                Le paiement du droit d'inscription et les écolages se fait via Mobile Banking sur le
                                numéro <strong>+261 32 78 337 36</strong>, au Nom de Monsieur Elia Gideon (Directeur Général de l'IMSAA).
            </p>
        </div>
    </div>
    <div class="row g-4">
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Référence de paiement <span
                    class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.reference"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.reference'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="example-text-input" name="firstname" placeholder="Référence de paiement">

            <?php $__errorArgs = ['newUser.reference'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">N° Téléphone de l'envoyeur <span
                    class="text-danger">*</span></label>
            <input type="text" wire:model="newUser.telenvoi"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.telenvoi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="example-text-input" name="lastname" placeholder="N° Téléphone de l'envoyeur">

            <?php $__errorArgs = ['newUser.telenvoi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="col-md-12 col-lg-6">
            <label class="form-label" for="example-text-input">Montant envoyé<span
                    class="text-danger">*</span></label>
            <input type="number" wire:model="newUser.montantenvoi"
                class="form-control form-control-alt <?php $__errorArgs = ['newUser.montantenvoi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                id="example-text-input" name="lastname" placeholder="Montant en ariary">

            <?php $__errorArgs = ['newUser.montantenvoi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
                    </div>
                <?php endif; ?>
    </div>

            <!-- Bottom Navigation (Simplified for mobile) -->
            <div class="bottom-navigation d-md-none mt-4 mb-4">
                <div class="d-flex justify-content-between">
                    <?php if($currentStep !== 'parcours'): ?>
                        <button type="button" class="btn btn-outline-secondary" wire:click="previousStep" wire:loading.attr="disabled" wire:target="previousStep">
                            <i class="fa fa-arrow-left me-1"></i>
                            <span wire:loading.remove wire:target="previousStep">Précédent</span>
                            <span wire:loading wire:target="previousStep" style="display: none;">
                                <i class="fa fa-spinner fa-spin me-1"></i> Chargement...
                            </span>
                        </button>
                    <?php else: ?>
                        <div></div>
                    <?php endif; ?>

                    <?php if($currentStep === 'paiement'): ?>
                        <button type="button" class="btn btn-success" wire:click="register" wire:loading.attr="disabled" wire:target="register" <?php echo e(!$this->canProceedToNextStep() || !$terms ? 'disabled' : ''); ?>>
                            <span wire:loading.remove wire:target="register">
                                <i class="fa fa-paper-plane me-1"></i> S'inscrire
                            </span>
                            <span wire:loading wire:target="register" style="display: none;">
                                <i class="fa fa-spinner fa-spin me-1"></i> Inscription...
                            </span>
                        </button>
                    <?php else: ?>
                        <button type="button" class="btn btn-primary" wire:click="nextStep" <?php echo e(!$this->canProceedToNextStep() ? 'disabled' : ''); ?> wire:loading.attr="disabled" wire:target="nextStep">
                            <span wire:loading.remove wire:target="nextStep">Suivant <i class="fa fa-arrow-right ms-1"></i></span>
                            <span wire:loading wire:target="nextStep" style="display: none;">
                                <i class="fa fa-spinner fa-spin me-1"></i> Chargement...
                            </span>
                        </button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Summary and Terms -->
            <?php if($currentStep === 'paiement'): ?>
                <!-- Registration Summary -->
                <div class="registration-summary mt-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fa fa-clipboard-list me-2"></i>Récapitulatif de votre inscription
                    </h5>

                    <div class="summary-sections">
                        <!-- Parcours Summary -->
                        <div class="summary-section">
                            <div class="summary-header">
                                <i class="fa fa-graduation-cap text-primary me-2"></i>
                                <strong>Formation choisie</strong>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-auto" wire:click="goToStep('parcours')">
                                    <i class="fa fa-edit"></i> Modifier
                                </button>
                            </div>
                            <div class="summary-content">
                                <?php if($niveau && $parcour): ?>
                                    <?php
                                        $niveauLabels = [1 => '1ère année', 2 => '2ème année (DTS)', 3 => '3ème année (Licence)', 4 => '4ème année (Master I)', 5 => '5ème année (Master II)'];
                                        $selectedParcour = $parcours->find($parcour);
                                    ?>
                                    <p class="mb-1"><strong>Niveau :</strong> <?php echo e($niveauLabels[$niveau] ?? 'Non défini'); ?></p>
                                    <p class="mb-0"><strong>Mention :</strong> <?php echo e($selectedParcour->nom ?? 'Non définie'); ?></p>
                                <?php else: ?>
                                    <p class="text-muted mb-0">Informations manquantes</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Personal Info Summary -->
                        <div class="summary-section">
                            <div class="summary-header">
                                <i class="fa fa-user text-info me-2"></i>
                                <strong>Informations personnelles</strong>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-auto" wire:click="goToStep('informations')">
                                    <i class="fa fa-edit"></i> Modifier
                                </button>
                            </div>
                            <div class="summary-content">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>Nom :</strong> <?php echo e($newUser['nom'] ?? 'Non renseigné'); ?></p>
                                        <p class="mb-1"><strong>Prénom :</strong> <?php echo e($newUser['prenom'] ?? 'Non renseigné'); ?></p>
                                        <p class="mb-1"><strong>Email :</strong> <?php echo e($newUser['email'] ?? 'Non renseigné'); ?></p>
                                        <p class="mb-1"><strong>Téléphone :</strong> <?php echo e($newUser['telephone1'] ?? 'Non renseigné'); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>Date de naissance :</strong> <?php echo e($newUser['date_naissance'] ?? 'Non renseignée'); ?></p>
                                        <p class="mb-1"><strong>Lieu de naissance :</strong> <?php echo e($newUser['lieu_naissance'] ?? 'Non renseigné'); ?></p>
                                        <p class="mb-1"><strong>Nationalité :</strong> <?php echo e($newUser['nationalite'] ?? 'Non renseignée'); ?></p>
                                        <p class="mb-0"><strong>Adresse :</strong> <?php echo e($newUser['adresse'] ?? 'Non renseignée'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Documents Summary -->
                        <div class="summary-section">
                            <div class="summary-header">
                                <i class="fa fa-file-alt text-success me-2"></i>
                                <strong>Documents fournis</strong>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-auto" wire:click="goToStep('documents')">
                                    <i class="fa fa-edit"></i> Modifier
                                </button>
                            </div>
                            <div class="summary-content">
                                <div class="documents-list">
                                    <?php if($niveau == 1): ?>
                                        <div class="document-item <?php echo e($relevebacc ? 'uploaded' : 'missing'); ?>">
                                            <i class="fa <?php echo e($relevebacc ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'); ?> me-2"></i>
                                            Relevé ou Diplôme BACC
                                            <?php if($relevebacc): ?>
                                                <small class="text-muted">(<?php echo e($relevebacc->getClientOriginalName()); ?>)</small>
                                            <?php endif; ?>
                                        </div>
                                    <?php elseif($niveau == 2): ?>
                                        <div class="document-item <?php echo e($cv ? 'uploaded' : 'missing'); ?>">
                                            <i class="fa <?php echo e($cv ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'); ?> me-2"></i>
                                            CV
                                            <?php if($cv): ?> <small class="text-muted">(<?php echo e($cv->getClientOriginalName()); ?>)</small> <?php endif; ?>
                                        </div>
                                        <div class="document-item <?php echo e($diplome ? 'uploaded' : 'missing'); ?>">
                                            <i class="fa <?php echo e($diplome ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'); ?> me-2"></i>
                                            Diplôme BACC
                                            <?php if($diplome): ?> <small class="text-muted">(<?php echo e($diplome->getClientOriginalName()); ?>)</small> <?php endif; ?>
                                        </div>
                                        <div class="document-item <?php echo e($releve1 ? 'uploaded' : 'missing'); ?>">
                                            <i class="fa <?php echo e($releve1 ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'); ?> me-2"></i>
                                            Relevé L1
                                            <?php if($releve1): ?> <small class="text-muted">(<?php echo e($releve1->getClientOriginalName()); ?>)</small> <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <!-- Add other levels as needed -->
                                </div>
                            </div>
                        </div>

                        <!-- Payment Summary -->
                        <div class="summary-section">
                            <div class="summary-header">
                                <i class="fa fa-credit-card text-warning me-2"></i>
                                <strong>Informations de paiement</strong>
                            </div>
                            <div class="summary-content">
                                <p class="mb-1"><strong>Référence :</strong> <?php echo e($newUser['reference'] ?? 'Non renseignée'); ?></p>
                                <p class="mb-1"><strong>Téléphone envoyeur :</strong> <?php echo e($newUser['telenvoi'] ?? 'Non renseigné'); ?></p>
                                <p class="mb-0"><strong>Montant :</strong> <?php echo e($newUser['montantenvoi'] ?? 'Non renseigné'); ?> Ar</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terms and conditions -->
                <div class="terms-section mt-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input <?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model="terms" type="checkbox"
                                        value="" id="signup-terms" wire:loading.attr="disabled">
                        <label class="form-check-label" for="signup-terms">
                            <strong>Je déclare avoir pris connaissance des conditions et de les accepter.</strong>
                        </label>
                    </div>

                    <div class="alert alert-info d-flex align-items-center" role="alert">
                        <div class="flex-shrink-0">
                            <i class="fa fa-fw fa-info-circle"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-0">
                                <strong>Important :</strong> Avant de soumettre votre inscription, veuillez bien vérifier que tous les champs
                                obligatoires sont remplis et que tous vos pièces jointes sont téléchargées.
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</form>

<!-- Document preview modal -->
<div class="modal fade" id="documentPreviewModal" tabindex="-1" wire:ignore.self>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if($previewDocument): ?>
                    <?php if(in_array($previewDocument->getClientOriginalExtension(), ['jpg', 'jpeg', 'png'])): ?>
                        <img src="<?php echo e($previewDocument->temporaryUrl()); ?>" class="img-fluid" alt="Aperçu">
                    <?php else: ?>
                        <div class="text-center">
                            <i class="fa fa-file-pdf fa-5x text-danger mb-3"></i>
                            <p class="mb-0"><?php echo e($previewDocument->getClientOriginalName()); ?></p>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>



<!-- File upload loading overlay -->
<div wire:loading wire:target="relevebacc, cv, diplome, releve1, releve2, releve3, releve4" class="position-relative">
    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.8); z-index: 999;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Téléchargement en cours...</span>
        </div>
    </div>
</div>

<?php if(session()->has('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fa fa-check-circle me-1"></i>
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if(session()->has('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fa fa-exclamation-circle me-1"></i>
        <?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Document preview
    window.addEventListener('showDocumentPreview', event => {
        var modal = new bootstrap.Modal(document.getElementById('documentPreviewModal'));
        modal.show();
    });

    // Enhanced step animations
    document.addEventListener('livewire:load', function () {
        Livewire.hook('message.processed', (message, component) => {
            // Reinitialize tooltips after Livewire updates
            setTimeout(() => {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl)
                });
            }, 100);

            // Animate steps
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.style.animationDelay = `${index * 0.1}s`;
                step.classList.add('fade-enter');
                setTimeout(() => {
                    step.classList.remove('fade-enter');
                }, 300 + (index * 100));
            });

            // Animate form groups
            const formGroups = document.querySelectorAll('.form-group-enhanced');
            formGroups.forEach((group, index) => {
                group.style.animationDelay = `${index * 0.05}s`;
                group.classList.add('fade-enter');
                setTimeout(() => {
                    group.classList.remove('fade-enter');
                }, 200 + (index * 50));
            });
        });
    });

    // Enhanced validation animations
    Livewire.on('validationError', () => {
        const invalidFields = document.querySelectorAll('.is-invalid');
        invalidFields.forEach((field, index) => {
            setTimeout(() => {
                field.classList.add('shake');
                setTimeout(() => {
                    field.classList.remove('shake');
                }, 500);
            }, index * 100);
        });
    });

    // Field update animations
    Livewire.on('fieldUpdated', (fieldName) => {
        const field = document.querySelector(`[wire\\:model*="${fieldName}"]`);
        if (field) {
            field.classList.add('field-updated');
            setTimeout(() => {
                field.classList.remove('field-updated');
            }, 300);
        }
    });

    // Progress update animations
    Livewire.on('progressUpdated', (percentage) => {
        const progressBar = document.querySelector('.progress-line');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }

        const badge = document.querySelector('.progress-percentage .badge');
        if (badge) {
            badge.textContent = Math.round(percentage) + '%';
            badge.classList.add('badge-updated');
            setTimeout(() => {
                badge.classList.remove('badge-updated');
            }, 300);
        }
    });

    // Phone number formatting
    document.addEventListener('input', function(e) {
        if (e.target.matches('[wire\\:model*="telephone"]')) {
            let value = e.target.value.replace(/[^\d+]/g, '');

            // Auto-add +261 for Madagascar numbers
            if (value.startsWith('0') && value.length === 10) {
                value = '+261' + value.substring(1);
            }

            // Format the number
            if (value.startsWith('+261') && value.length === 13) {
                const formatted = value.replace(/(\+261)(\d{2})(\d{2})(\d{3})(\d{2})/, '$1 $2 $3 $4 $5');
                if (e.target.value !== formatted) {
                    e.target.value = formatted;
                    e.target.dispatchEvent(new Event('input'));
                }
            }
        }
    });

    // File upload enhancements
    document.addEventListener('change', function(e) {
        if (e.target.type === 'file' && e.target.files.length > 0) {
            const file = e.target.files[0];
            const maxSize = 5 * 1024 * 1024; // 5MB

            if (file.size > maxSize) {
                alert('Le fichier est trop volumineux. Taille maximale : 5MB');
                e.target.value = '';
                return;
            }

            // Show upload loading
            const uploadZone = e.target.closest('.file-upload-enhanced');
            if (uploadZone) {
                const loadingDiv = uploadZone.querySelector('.upload-loading');
                if (loadingDiv) {
                    loadingDiv.style.display = 'flex';
                }
                uploadZone.classList.add('uploading');
            }
        }
    });

    // Hide upload loading when Livewire finishes processing
    Livewire.hook('message.processed', (message, component) => {
        // Hide all upload loading indicators
        document.querySelectorAll('.upload-loading').forEach(loading => {
            loading.style.display = 'none';
        });

        // Remove uploading class
        document.querySelectorAll('.file-upload-enhanced').forEach(zone => {
            zone.classList.remove('uploading');
        });
    });

    // Smooth scroll to errors
    Livewire.on('scrollToError', () => {
        setTimeout(() => {
            const firstError = document.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                firstError.focus();
            }
        }, 100);
    });

    // Enhanced loading states management
    document.addEventListener('click', function(e) {
        // Handle button clicks with loading states
        if (e.target.matches('[wire\\:click]') || e.target.closest('[wire\\:click]')) {
            const button = e.target.matches('[wire\\:click]') ? e.target : e.target.closest('[wire\\:click]');
            const loadingSpan = button.querySelector('[wire\\:loading]');
            const normalSpan = button.querySelector('[wire\\:loading\\.remove]');

            if (loadingSpan && normalSpan) {
                // Show loading state immediately
                normalSpan.style.display = 'none';
                loadingSpan.style.display = 'flex';
                button.disabled = true;
            }
        }
    });

    // Auto-save indication
    let autoSaveTimeout;
    Livewire.hook('message.sent', (message, component) => {
        clearTimeout(autoSaveTimeout);
        const indicator = document.querySelector('.auto-save-indicator');
        if (indicator && (message.updateQueue.some(update =>
            update.payload.method === 'syncInput' &&
            (update.payload.id.includes('newUser') || update.payload.id.includes('niveau') || update.payload.id.includes('parcour'))
        ))) {
            indicator.querySelector('.indicator-text').textContent = 'Sauvegarde...';
            indicator.classList.add('saving');
            indicator.style.display = 'block';
        }
    });

    Livewire.hook('message.processed', (message, component) => {
        // Reset button states
        document.querySelectorAll('[wire\\:click]').forEach(button => {
            const loadingSpan = button.querySelector('[wire\\:loading]');
            const normalSpan = button.querySelector('[wire\\:loading\\.remove]');

            if (loadingSpan && normalSpan) {
                normalSpan.style.display = '';
                loadingSpan.style.display = 'none';
                button.disabled = false;
            }
        });

        // Auto-save indicator
        const indicator = document.querySelector('.auto-save-indicator');
        if (indicator && indicator.classList.contains('saving')) {
            indicator.querySelector('.indicator-text').textContent = 'Sauvegardé';
            indicator.classList.remove('saving');
            indicator.classList.add('saved');

            autoSaveTimeout = setTimeout(() => {
                indicator.classList.remove('saved');
                indicator.style.display = 'none';
            }, 2000);
        }
    });

    // File upload drag and drop
    document.addEventListener('dragover', function(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.file-drop-zone');
        if (dropZone) {
            dropZone.classList.add('dragover');
        }
    });

    document.addEventListener('dragleave', function(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.file-drop-zone');
        if (dropZone && !dropZone.contains(e.relatedTarget)) {
            dropZone.classList.remove('dragover');
        }
    });

    document.addEventListener('drop', function(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.file-drop-zone');
        if (dropZone) {
            dropZone.classList.remove('dragover');
            const fileInput = dropZone.querySelector('input[type="file"]');
            if (fileInput && e.dataTransfer.files.length > 0) {
                fileInput.files = e.dataTransfer.files;
                fileInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
    });
</script>
<?php $__env->stopPush(); ?>

<style>
/* Enhanced Progress Steps */
.progress-steps {
    position: relative;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.progress-line-container {
    position: absolute;
    top: 50px;
    left: 50%;
    right: 50%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    transform: translateX(-50%);
    width: calc(100% - 120px);
    z-index: 0;
}

.progress-line {
    height: 100%;
    background: linear-gradient(90deg, #0d6efd 0%, #6610f2 100%);
    border-radius: 2px;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.progress-line::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-20px); }
    100% { transform: translateX(20px); }
}

.steps-container {
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.step {
    text-align: center;
    position: relative;
    flex: 1;
    max-width: 200px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-icon-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 12px;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #ffffff;
    border: 3px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 18px;
    color: #6c757d;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.step.completed .step-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.step.current .step-icon {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
    border-color: #0d6efd;
    color: white;
    transform: scale(1.15);
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
}

.step.disabled .step-icon {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #adb5bd;
    opacity: 0.6;
}

.step-pulse {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 2px solid #0d6efd;
    border-radius: 50%;
    animation: pulse 2s infinite;
    opacity: 0.6;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

.step-content {
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.95rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    transition: all 0.3s ease;
}

.step-description {
    font-size: 0.75rem;
    color: #6c757d;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.step.completed .step-label {
    color: #28a745;
    font-weight: 700;
}

.step.current .step-label {
    color: #0d6efd;
    font-weight: 700;
}

.step.disabled .step-label,
.step.disabled .step-description {
    color: #adb5bd;
}

.step-checkmark {
    position: absolute;
    top: -5px;
    right: 20px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
    animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.progress-info {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.progress-text {
    font-weight: 500;
}

.progress-percentage .badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
}

/* Hover Effects */
.step:not(.disabled):hover {
    transform: translateY(-2px);
}

.step:not(.disabled):hover .step-icon {
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.step:not(.disabled):hover .step-label {
    color: #0d6efd;
}

/* Enhanced Navigation Styles */
.navigation-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
}

.nav-button-wrapper {
    min-width: 200px;
}

.btn-nav {
    min-height: 60px;
    border-radius: 10px;
    border-width: 2px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-nav:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-nav:hover:before {
    left: 100%;
}

.btn-nav .btn-text {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.btn-nav .btn-main {
    font-size: 1rem;
    font-weight: 600;
}

.btn-nav .btn-sub {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 2px;
}

.btn-nav:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.btn-nav:disabled {
    transform: none;
    box-shadow: none;
    opacity: 0.6;
}

.btn-submit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-submit:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    box-shadow: 0 6px 25px rgba(40, 167, 69, 0.4);
}

.step-validation {
    padding: 10px 20px;
    border-radius: 8px;
    background: white;
    border: 1px solid #e9ecef;
}

.validation-success {
    animation: successPulse 0.6s ease;
}

.validation-pending {
    animation: pendingPulse 2s infinite;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes pendingPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.progress-summary {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.summary-item {
    padding: 10px;
}

.summary-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0d6efd;
    line-height: 1;
}

.summary-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
    /* Progress Steps Mobile */
    .progress-steps {
        padding: 15px 10px;
        margin-bottom: 2rem;
    }

    .steps-container {
        flex-direction: column;
        gap: 15px;
    }

    .progress-line-container {
        display: none;
    }

    .step {
        max-width: none;
        display: flex;
        align-items: center;
        text-align: left;
        padding: 12px 15px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        border: 1px solid #f1f3f4;
        margin-bottom: 8px;
    }

    .step.current {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-color: #0d6efd;
        box-shadow: 0 4px 15px rgba(13, 110, 253, 0.15);
    }

    .step.completed {
        background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        border-color: #28a745;
    }

    .step-icon-wrapper {
        margin-bottom: 0;
        margin-right: 12px;
    }

    .step-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .step-content {
        flex: 1;
    }

    .step-label {
        font-size: 0.9rem;
        margin-bottom: 2px;
    }

    .step-description {
        font-size: 0.7rem;
        line-height: 1.2;
    }

    .step-checkmark {
        position: static;
        margin-left: auto;
        width: 18px;
        height: 18px;
        font-size: 9px;
    }

    /* Navigation Mobile */
    .navigation-container {
        padding: 15px;
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #e9ecef;
        margin: 0 -15px;
        border-radius: 0;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }

    .nav-button-wrapper {
        min-width: auto;
        flex: 1;
    }

    .btn-nav {
        width: 100%;
        min-height: 45px;
        font-size: 0.9rem;
        padding: 8px 16px;
    }

    .btn-nav .btn-sub {
        display: none;
    }

    .btn-nav .btn-main {
        font-size: 0.9rem;
    }

    .step-validation {
        order: -1;
        margin-bottom: 12px;
        text-align: center;
        padding: 8px 15px;
        border-radius: 6px;
    }

    .navigation-container .d-flex {
        flex-direction: column;
        gap: 12px;
    }

    .progress-summary {
        margin-top: 12px;
        padding-top: 12px;
    }

    .progress-summary .row {
        margin: 0;
    }

    .progress-summary .col-4 {
        padding: 3px;
    }

    .summary-number {
        font-size: 1.1rem;
    }

    .summary-label {
        font-size: 0.65rem;
    }

    /* Form Mobile Optimizations */
    .form-content {
        padding: 0 5px;
        min-height: auto;
    }

    .row.g-4 {
        --bs-gutter-x: 1rem;
        --bs-gutter-y: 1rem;
    }

    .col-md-12.col-lg-6 {
        margin-bottom: 1rem;
    }

    /* Mobile Form Groups */
    .form-group-enhanced {
        margin-bottom: 1.25rem;
    }

    .form-label {
        font-size: 0.9rem;
        margin-bottom: 0.4rem;
    }

    .form-control-enhanced {
        padding: 10px 40px 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
        border-radius: 6px;
    }

    .input-feedback {
        right: 12px;
        font-size: 1rem;
    }

    .input-group .input-feedback {
        right: 40px;
    }

    /* Mobile File Upload */
    .file-drop-zone {
        padding: 1.25rem 1rem;
    }

    .drop-zone-icon .fa-3x {
        font-size: 2rem !important;
    }

    .drop-zone-text h6 {
        font-size: 1rem;
    }

    .drop-zone-text p {
        font-size: 0.85rem;
    }

    .file-preview-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
        padding: 0.75rem;
    }

    .file-info {
        width: 100%;
    }

    .file-actions {
        align-self: stretch;
        justify-content: center;
        gap: 0.75rem;
    }

    .file-actions .btn {
        flex: 1;
        max-width: 120px;
    }

    .file-thumbnail,
    .file-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.75rem;
    }

    .file-name {
        font-size: 0.85rem;
    }

    .file-size {
        font-size: 0.75rem;
    }

    /* Mobile Tooltips */
    [data-bs-toggle="tooltip"] {
        display: none; /* Hide tooltips on mobile for cleaner UI */
    }

    /* Mobile Dropdown */
    .dropdown-menu {
        font-size: 0.85rem;
    }

    .dropdown-item {
        padding: 0.6rem 1rem;
    }

    /* Mobile Auto-save Indicator */
    .auto-save-indicator {
        top: 10px;
        right: 10px;
        font-size: 0.75rem;
        padding: 6px 12px;
    }

    /* Mobile Bottom Navigation */
    .bottom-navigation {
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #e9ecef;
        margin: 0 -15px;
        padding: 15px;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }

    .bottom-navigation .btn {
        min-height: 45px;
        font-size: 0.9rem;
    }
}

/* Tablet Responsive (768px - 992px) */
@media (min-width: 768px) and (max-width: 992px) {
    .progress-steps {
        padding: 18px 15px;
    }

    .step-icon {
        width: 45px;
        height: 45px;
        font-size: 17px;
    }

    .step-label {
        font-size: 0.9rem;
    }

    .step-description {
        font-size: 0.73rem;
    }

    .navigation-container {
        padding: 18px;
    }

    .btn-nav {
        min-height: 55px;
    }

    .form-control-enhanced {
        padding: 11px 42px 11px 14px;
    }
}

/* Large Mobile (576px - 768px) */
@media (min-width: 576px) and (max-width: 768px) {
    .steps-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
    }

    .step {
        flex-direction: column;
        text-align: center;
        padding: 15px 10px;
    }

    .step-icon-wrapper {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .step-content {
        flex: none;
    }

    .step-checkmark {
        position: absolute;
        top: 5px;
        right: 5px;
        margin-left: 0;
    }
}

.fade-enter {
    opacity: 0;
    transform: translateY(10px);
}

.fade-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms, transform 300ms;
}

.fade-exit {
    opacity: 1;
    transform: translateY(0);
}

.fade-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 300ms, transform 300ms;
}

.step {
    transition: all 0.3s ease;
}

.step:hover {
    transform: translateY(-2px);
}

.step.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: none;
}

.document-preview {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.document-preview:hover {
    transform: scale(1.02);
}

.document-preview img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.document-preview .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.document-preview:hover .overlay {
    opacity: 1;
}

.validation-message {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.validation-message.valid {
    color: #198754;
}

.validation-message.invalid {
    color: #dc3545;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.form-content {
    min-height: 400px;
}

/* Loading states */
.btn:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading overlay animations */
.position-fixed {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.bg-white {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Enhanced Form Styles */
.form-group-enhanced {
    margin-bottom: 1.5rem;
    position: relative;
}

.input-wrapper {
    position: relative;
}

.form-control-enhanced {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 45px 12px 15px;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    color: #495057;
}

.form-control-enhanced:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    background: #ffffff;
    color: #212529;
    outline: none;
}

.form-control-enhanced.is-valid {
    border-color: #198754;
    background: #ffffff;
    padding-right: 45px;
    color: #212529;
}

.form-control-enhanced.is-valid:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.15);
    color: #212529;
}

.form-control-enhanced.is-invalid {
    border-color: #dc3545;
    background: #ffffff;
    padding-right: 45px;
    color: #212529;
}

.form-control-enhanced.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
    color: #212529;
}

.input-feedback {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
    font-size: 1.1rem;
}

.input-group .input-feedback {
    right: 50px;
}

.form-feedback {
    margin-top: 0.5rem;
}

.valid-feedback {
    color: #198754 !important;
    font-size: 0.875rem;
    font-weight: 600;
    animation: slideInUp 0.3s ease;
}

.invalid-feedback {
    color: #dc3545 !important;
    font-size: 0.875rem;
    font-weight: 600;
    animation: shake 0.5s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-label {
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #495057;
}

.form-text {
    color: #6c757d !important;
    font-size: 0.875rem;
}

.text-muted {
    color: #6c757d !important;
}

.input-group-text {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    color: #6c757d;
}

.form-control-enhanced:focus + .input-group-text,
.input-group:focus-within .input-group-text {
    border-color: #0d6efd;
    background: #f8f9fa;
}

.dropdown-menu {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #0d6efd;
}

.dropdown-header {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    padding: 0.5rem 1rem 0.25rem;
}

.dropdown-item-text {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

/* Loading states for inputs */
.form-control-enhanced[wire\:loading] {
    background-image: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%236c757d' fill-opacity='0.3'%3e%3cpath d='M10 3v3l4-4-4-4v3c-4.42 0-8 3.58-8 8s3.58 8 8 8c1.57 0 3.04-.46 4.28-1.25l-1.32-1.32C11.16 11.99 10.64 12 10 12c-2.21 0-4-1.79-4-4s1.79-4 4-4z'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px 16px;
    animation: spin 1s linear infinite;
}

/* Tooltip improvements */
[data-bs-toggle="tooltip"] {
    cursor: help;
    color: #6c757d;
    transition: color 0.2s ease;
}

[data-bs-toggle="tooltip"]:hover {
    color: #0d6efd;
}

/* Form animations */
.form-group-enhanced {
    animation: fadeInUp 0.4s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus improvements */
.form-control-enhanced:focus {
    outline: none;
    transform: translateY(-1px);
}

.input-group:focus-within {
    transform: translateY(-1px);
}

/* Enhanced File Upload Styles */
.file-upload-enhanced {
    margin-bottom: 1.5rem;
}

.file-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.file-drop-zone:hover {
    border-color: #0d6efd;
    background: #e7f1ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.15);
}

.file-drop-zone.dragover {
    border-color: #198754;
    background: #d1e7dd;
    transform: scale(1.02);
    box-shadow: 0 6px 25px rgba(25, 135, 84, 0.2);
}

.file-drop-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(13, 110, 253, 0.1), transparent);
    transition: left 0.6s;
}

.file-drop-zone:hover::before {
    left: 100%;
}

.drop-zone-content {
    position: relative;
    z-index: 1;
}

.drop-zone-icon {
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.drop-zone-text h6 {
    color: #212529;
    font-weight: 600;
}

.drop-zone-text p {
    margin-bottom: 0.5rem;
    color: #495057;
}

.drop-zone-text small {
    color: #6c757d;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-preview {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: white;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.file-preview-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f1f3f4;
}

.file-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.file-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    margin-right: 1rem;
    font-size: 1.5rem;
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.file-size {
    font-size: 0.875rem;
    color: #6c757d;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.file-actions .btn {
    border-radius: 6px;
    padding: 0.375rem 0.75rem;
}

.upload-progress {
    padding: 1rem;
    background: #f8f9fa;
}

.upload-progress .progress {
    height: 6px;
    border-radius: 3px;
    margin-bottom: 0.5rem;
}

.upload-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    z-index: 10;
}

.loading-content {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-weight: 500;
    color: #0d6efd;
}

/* File type specific styles */
.file-icon .fa-file-pdf {
    color: #dc3545;
}

.file-icon .fa-file-image {
    color: #28a745;
}

.file-icon .fa-file-alt {
    color: #6c757d;
}

/* Animation for file upload success */
.file-preview {
    animation: slideInUp 0.4s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Drag and drop animations */
.file-drop-zone {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-drop-zone.dragover .drop-zone-icon {
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* Mobile improvements */
@media (max-width: 768px) {
    .form-control-enhanced {
        padding: 10px 40px 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .input-feedback {
        right: 12px;
    }

    .input-group .input-feedback {
        right: 45px;
    }

    .file-drop-zone {
        padding: 1.5rem 1rem;
    }

    .drop-zone-icon .fa-3x {
        font-size: 2rem !important;
    }

    .file-preview-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .file-actions {
        align-self: stretch;
        justify-content: center;
    }

    .file-thumbnail,
    .file-icon {
        width: 40px;
        height: 40px;
        margin-right: 0.75rem;
    }
}

/* Enhanced Animations and Interactions */
.field-updated {
    animation: fieldPulse 0.3s ease;
}

@keyframes fieldPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.badge-updated {
    animation: badgeBounce 0.3s ease;
}

@keyframes badgeBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.uploading .file-drop-zone {
    border-color: #0d6efd;
    background: linear-gradient(45deg, #f8f9ff, #e3f2fd);
    animation: uploadPulse 1s ease-in-out infinite;
}

@keyframes uploadPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Auto-save indicator */
.auto-save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 0.875rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    opacity: 0;
    transform: translateX(100px);
    transition: all 0.3s ease;
}

.auto-save-indicator.saving {
    opacity: 1;
    transform: translateX(0);
    color: #0d6efd;
    border-color: #0d6efd;
}

.auto-save-indicator.saved {
    opacity: 1;
    transform: translateX(0);
    color: #28a745;
    border-color: #28a745;
}

/* Enhanced focus states */
.form-control-enhanced:focus,
.file-drop-zone:focus-within {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-control-enhanced {
        border-width: 3px;
    }

    .step-icon {
        border-width: 3px;
    }

    .file-drop-zone {
        border-width: 3px;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .progress-steps {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        color: #e2e8f0;
    }

    .form-control-enhanced {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .file-drop-zone {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}

/* Print styles */
@media print {
    .navigation-container,
    .progress-steps,
    .file-actions,
    .auto-save-indicator {
        display: none !important;
    }

    .form-content {
        break-inside: avoid;
    }
}

/* Loading overlay improvements */
.upload-loading {
    backdrop-filter: blur(2px);
}

/* Micro-interactions */
.btn-nav:active {
    transform: translateY(-1px) scale(0.98);
}

.step:active {
    transform: translateY(0) scale(0.98);
}

.file-drop-zone:active {
    transform: scale(0.98);
}

/* Smooth transitions for all interactive elements */
.btn, .form-control, .step, .file-drop-zone, .file-preview {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced error states */
.is-invalid {
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success states */
.is-valid {
    animation: successGlow 0.6s ease-in-out;
}

@keyframes successGlow {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4); }
    50% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* Registration Summary Styles */
.registration-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.registration-summary h5 {
    color: #495057;
    font-weight: 700;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
}

.summary-sections {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.summary-section {
    background: white;
    border-radius: 10px;
    border: 1px solid #f1f3f4;
    overflow: hidden;
    transition: all 0.3s ease;
}

.summary-section:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transform: translateY(-1px);
}

.summary-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.25rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.summary-header strong {
    flex: 1;
    color: #495057;
    font-size: 1rem;
}

.summary-content {
    padding: 1.25rem;
}

.summary-content p {
    line-height: 1.6;
    color: #495057;
}

.summary-content strong {
    color: #343a40;
    font-weight: 600;
}

.documents-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.document-item {
    padding: 0.75rem;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.document-item.uploaded {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
    color: #155724;
}

.document-item.missing {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
    color: #721c24;
}

.document-item small {
    display: block;
    margin-top: 0.25rem;
    font-style: italic;
}

.terms-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.terms-section .form-check {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.terms-section .form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.terms-section .form-check-label {
    color: #495057;
    cursor: pointer;
    user-select: none;
}

.terms-section .alert {
    margin-bottom: 0;
    border-radius: 8px;
}

/* Enhanced Auto-save Indicator */
.auto-save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 25px;
    padding: 10px 16px;
    font-size: 0.875rem;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    z-index: 1050;
    opacity: 0;
    transform: translateX(100px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.auto-save-indicator.saving {
    opacity: 1;
    transform: translateX(0);
    color: #0d6efd;
    border-color: #0d6efd;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.auto-save-indicator.saving .indicator-text::after {
    content: '...';
    animation: dots 1.5s infinite;
}

.auto-save-indicator.saved {
    opacity: 1;
    transform: translateX(0);
    color: #28a745;
    border-color: #28a745;
    background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Mobile Summary Styles */
@media (max-width: 768px) {
    .registration-summary {
        padding: 1rem;
        margin: 0 -5px;
    }

    .registration-summary h5 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .summary-sections {
        gap: 1rem;
    }

    .summary-header {
        padding: 0.75rem 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .summary-header .btn {
        align-self: stretch;
        font-size: 0.8rem;
    }

    .summary-content {
        padding: 1rem;
    }

    .summary-content .row {
        margin: 0;
    }

    .summary-content .col-md-6 {
        padding: 0;
        margin-bottom: 1rem;
    }

    .document-item {
        padding: 0.6rem;
        font-size: 0.9rem;
    }

    .terms-section {
        padding: 1rem;
        margin: 0 -5px;
    }

    .terms-section .form-check {
        padding: 0.75rem;
    }

    .auto-save-indicator {
        top: 10px;
        right: 10px;
        font-size: 0.75rem;
        padding: 8px 12px;
        border-radius: 20px;
    }
}

/* Print styles for summary */
@media print {
    .registration-summary {
        break-inside: avoid;
        box-shadow: none;
        border: 2px solid #000;
    }

    .summary-header .btn {
        display: none;
    }

    .terms-section .alert {
        display: none;
    }
}

/* Accessibility improvements */
.summary-section:focus-within {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.document-item:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .summary-section {
        border-width: 2px;
    }

    .document-item {
        border-width: 2px;
    }

    .terms-section {
        border-width: 3px;
    }
}
</style>
<?php /**PATH C:\xampp\htdocs\ImsaaFoad\resources\views/livewire/multi-step-register.blade.php ENDPATH**/ ?>