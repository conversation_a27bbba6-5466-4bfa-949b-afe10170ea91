{"__meta": {"id": "Xedd283beb018ab552f309ca29027250b", "datetime": "2025-07-01 18:21:26", "utime": 1751383286.927961, "method": "POST", "uri": "/livewire/upload-file?expires=1751383584&signature=e07ad20394c8c3951ec234989ee1d2c8ef00e0b692d880c84b82d8b418a79705", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751383286.062007, "end": 1751383286.92799, "duration": 0.8659830093383789, "duration_str": "866ms", "measures": [{"label": "Booting", "start": 1751383286.062007, "relative_start": 0, "end": 1751383286.671701, "relative_end": 1751383286.671701, "duration": 0.6096940040588379, "duration_str": "610ms", "params": [], "collector": null}, {"label": "Application", "start": 1751383286.672496, "relative_start": 0.6104891300201416, "end": 1751383286.927992, "relative_end": 2.1457672119140625e-06, "duration": 0.2554960250854492, "duration_str": "255ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26436560, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/upload-file", "controller": "Livewire\\Controllers\\FileUploadHandler@handle", "as": "livewire.upload-file", "middleware": "web", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php&line=19\">\\vendor\\livewire\\livewire\\src\\Controllers\\FileUploadHandler.php:19-28</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:5 [\n  \"newUser\" => array:10 [\n    \"nom\" => \"qdfqs\"\n    \"prenom\" => \"qsdfqsdf\"\n    \"date_naissance\" => \"2002-01-03\"\n    \"sexe\" => \"H\"\n    \"lieu_naissance\" => \"qsdf\"\n    \"adresse\" => \"qsdf\"\n    \"nationalite\" => \"qsdf\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+261 32 55 516 50\"\n    \"cin\" => \"123412341234\"\n  ]\n  \"niveau\" => \"1\"\n  \"parcour\" => 1\n  \"currentStep\" => \"documents\"\n  \"files_uploaded\" => array:7 [\n    \"cv\" => false\n    \"diplome\" => false\n    \"relevebacc\" => false\n    \"releve1\" => false\n    \"releve2\" => false\n    \"releve3\" => false\n    \"releve4\" => false\n  ]\n]"}, "request": {"path_info": "/livewire/upload-file", "status_code": "<pre class=sf-dump id=sf-dump-841736317 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-841736317\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1682962663 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1751383584</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">e07ad20394c8c3951ec234989ee1d2c8ef00e0b692d880c84b82d8b418a79705</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682962663\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1109618629 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1109618629\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-452270292 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">94072</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryx6qat7XADBCEkomJ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkJIUitYRFRFc3BNSmVjTWg2Q1VxQ2c9PSIsInZhbHVlIjoiZjlpbDd1ME9KcVBYVXlzdER5MnE5UDRHL1R6NDRTdGFPZ3pHRXlrNmFBUXM5SzRXVmNTOHA5MjJGNUFHTmtyV1NDSys2eFBObVJBQ3Ryd2pVcDh2cGlvZXAzc2w4YWJNMUVqMzlrd2MxVUFaL0dRSjQ2Y29DMVFRVXlNTTNEajkiLCJtYWMiOiJmOGNkOGVmY2ExNTdmYTUwMTc2ZWYwZmYxMDFiZjdmZGFmMmI4ZDAwOGU1MDcwYTdmZTUzNzQwZmQ5MzZhMmY2IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImpKMHM5NTYwRHIxemVPNFNjZUttNGc9PSIsInZhbHVlIjoiSlpya0EzNmVhUXhmWWF2NE9aQjM0Zmp3bDVjWEFjU2JBZTdiNWxHbXFxM3B6Q1ZFRHYwZ0sxeFBtVzB1UlhzL1IyaWNQSEwxSzlGT0w1SzdBM0ZYOUZJb21nYWFUN3RnZEpHb2Fva05hcXpUNVlEcHJmWGcvdTF6a0E0cjlLckoiLCJtYWMiOiJiNGNjZTViMDNjMzM1Y2NiYjVhYTU1N2VmYTgxMGE2NmZhYTQyMjc2YzVkZGUzNTQyOTQ0MDA2ZWY4NWM4ZDAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452270292\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-233626816 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60445</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"115 characters\">/livewire/upload-file?expires=1751383584&amp;signature=e07ad20394c8c3951ec234989ee1d2c8ef00e0b692d880c84b82d8b418a79705</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/upload-file</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"93 characters\">expires=1751383584&amp;signature=e07ad20394c8c3951ec234989ee1d2c8ef00e0b692d880c84b82d8b418a79705</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">94072</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">94072</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryx6qat7XADBCEkomJ</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryx6qat7XADBCEkomJ</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkJIUitYRFRFc3BNSmVjTWg2Q1VxQ2c9PSIsInZhbHVlIjoiZjlpbDd1ME9KcVBYVXlzdER5MnE5UDRHL1R6NDRTdGFPZ3pHRXlrNmFBUXM5SzRXVmNTOHA5MjJGNUFHTmtyV1NDSys2eFBObVJBQ3Ryd2pVcDh2cGlvZXAzc2w4YWJNMUVqMzlrd2MxVUFaL0dRSjQ2Y29DMVFRVXlNTTNEajkiLCJtYWMiOiJmOGNkOGVmY2ExNTdmYTUwMTc2ZWYwZmYxMDFiZjdmZGFmMmI4ZDAwOGU1MDcwYTdmZTUzNzQwZmQ5MzZhMmY2IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImpKMHM5NTYwRHIxemVPNFNjZUttNGc9PSIsInZhbHVlIjoiSlpya0EzNmVhUXhmWWF2NE9aQjM0Zmp3bDVjWEFjU2JBZTdiNWxHbXFxM3B6Q1ZFRHYwZ0sxeFBtVzB1UlhzL1IyaWNQSEwxSzlGT0w1SzdBM0ZYOUZJb21nYWFUN3RnZEpHb2Fva05hcXpUNVlEcHJmWGcvdTF6a0E0cjlLckoiLCJtYWMiOiJiNGNjZTViMDNjMzM1Y2NiYjVhYTU1N2VmYTgxMGE2NmZhYTQyMjc2YzVkZGUzNTQyOTQ0MDA2ZWY4NWM4ZDAwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751383286.062</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751383286</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233626816\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1730791671 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3MQqMzZAJmil6GWNRTwZQGoMQJ2F3HRZ41OmdrAo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730791671\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1030490492 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:21:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdsSlY1TEtwS0dsRFR4SURlVW1IS3c9PSIsInZhbHVlIjoieHVHR3NmOVZQVFV4Z3RQanVKVTl5TVNCTjBOblR4S214dkw4T2lNaitydzRrRWFldzROZ2RaWEtuQUtlRnJXQzJ6TTlTSVJmTWRuZXJVZTNvVGp4MjMwOEx3cmp5dkdKTEhlWFdURDllM2RRQkhQM1Z6d2UrdTJUb0JCK0c3SWoiLCJtYWMiOiI1OWU1NGE3YmI4YmRkN2U5OTMzYzhhOGNlOWRjNmQzZjNhMGMwNWQzNjRmZmMyYjU2NzFiNDQxODE0OGFkOTZlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:21:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6InROaUJjcy9ESTNIbE13N0EyMGdwZGc9PSIsInZhbHVlIjoic3hjWWtaSjU5QWxQOHFMbmZIVTlDL24xOGVyRmZ6d3RCVEFaTEtQOENadlhkYmc4MlRqMVdWbjllUVhxNVdFY3RVT093YmVLais2dkw4U0g0U3FWcjUyRStsQzlJT3RIN1o0eEhJRjBsN1FSd3B2NVRBNWJhbk1zTnplcFBoN0ciLCJtYWMiOiI2ZjJlYzJmMjYzYjcyOGY1YjlhNTQxOTIwNjlkODNmOWM1MzkwOTVmMTBlYTIxMTcxODc5YjFmYTUzYmRlNzFiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 17:21:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdsSlY1TEtwS0dsRFR4SURlVW1IS3c9PSIsInZhbHVlIjoieHVHR3NmOVZQVFV4Z3RQanVKVTl5TVNCTjBOblR4S214dkw4T2lNaitydzRrRWFldzROZ2RaWEtuQUtlRnJXQzJ6TTlTSVJmTWRuZXJVZTNvVGp4MjMwOEx3cmp5dkdKTEhlWFdURDllM2RRQkhQM1Z6d2UrdTJUb0JCK0c3SWoiLCJtYWMiOiI1OWU1NGE3YmI4YmRkN2U5OTMzYzhhOGNlOWRjNmQzZjNhMGMwNWQzNjRmZmMyYjU2NzFiNDQxODE0OGFkOTZlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:21:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6InROaUJjcy9ESTNIbE13N0EyMGdwZGc9PSIsInZhbHVlIjoic3hjWWtaSjU5QWxQOHFMbmZIVTlDL24xOGVyRmZ6d3RCVEFaTEtQOENadlhkYmc4MlRqMVdWbjllUVhxNVdFY3RVT093YmVLais2dkw4U0g0U3FWcjUyRStsQzlJT3RIN1o0eEhJRjBsN1FSd3B2NVRBNWJhbk1zTnplcFBoN0ciLCJtYWMiOiI2ZjJlYzJmMjYzYjcyOGY1YjlhNTQxOTIwNjlkODNmOWM1MzkwOTVmMTBlYTIxMTcxODc5YjFmYTUzYmRlNzFiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 17:21:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030490492\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-456853760 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">utYqEazOPNQYzfRmgpTQ6hrRFkbmEXbrXu361Ljn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">qdfqs</span>\"\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">qsdfqsdf</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2002-01-03</span>\"\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qsdf</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+261 32 55 516 50</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341234</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">documents</span>\"\n    \"<span class=sf-dump-key>files_uploaded</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>cv</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>diplome</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>relevebacc</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>releve1</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>releve2</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>releve3</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>releve4</span>\" => <span class=sf-dump-const>false</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456853760\", {\"maxDepth\":0})</script>\n"}}