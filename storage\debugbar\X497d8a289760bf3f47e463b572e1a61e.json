{"__meta": {"id": "X497d8a289760bf3f47e463b572e1a61e", "datetime": "2025-07-01 17:31:44", "utime": 1751380304.89909, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380303.376248, "end": 1751380304.899146, "duration": 1.5228981971740723, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1751380303.376248, "relative_start": 0, "end": 1751380304.513071, "relative_end": 1751380304.513071, "duration": 1.1368231773376465, "duration_str": "1.14s", "params": [], "collector": null}, {"label": "Application", "start": 1751380304.515667, "relative_start": 1.1394190788269043, "end": 1751380304.89915, "relative_end": 3.814697265625e-06, "duration": 0.3834829330444336, "duration_str": "383ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23311016, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/auth/login.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1209277083 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1209277083\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-290527027 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-290527027\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1091668635 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1091668635\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-251093430 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IldzZXI4N0J3cHlYaVUycUpsVEFsRXc9PSIsInZhbHVlIjoiSktpOEhCOVNrYzh0bDJXdXRYUjhGTlZpMGJaVzlVdVJTMEp5M1FGdXhvaGNzdGZveVNJLzdMb1hrd2E4YmNZbGxXZkhrdW5VY1Y4L2ZqWklLZXIxaVp5cUx4RXRPWDRoWitKbXJGUldjYTBXRHB5WGkzU0todkFJMUtzb050bloiLCJtYWMiOiJkMWE2Y2I5NTFmNTU4M2E5OGJjYjZkNTJmYmRiN2ViMGE4NTYzNGI4M2MxNGFmZGIzY2RhN2M5ZjIxMmNjYmU1IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkpTMGk1SUZJWkR5VXZjcmRRc2lUY0E9PSIsInZhbHVlIjoiNHZucHpxU0E0QnljeE1mS2RaS09sMzAzcnFmVDRxWGhpUUEvaDRWRC95blAvcW9KdHNsNlNpZ1lYQkxmSU1HUFVPdXhqeVQwNEFHOHQ1cGhpMzVzaEVPUSsvR1pXb2JQbEp4U1ZmdjczcWQ1aDRDY1VhcDJSTEljcS96R3FEOUEiLCJtYWMiOiIwYjY3NWZhYTkxNzRjZDViNmNlNWRlY2Y1Nzc4OGRkZTRmNjNhOWFlNzQ1MGMwMDg5NWY0NjgyNDQyMjY5ZGE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251093430\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-87812372 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57247</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IldzZXI4N0J3cHlYaVUycUpsVEFsRXc9PSIsInZhbHVlIjoiSktpOEhCOVNrYzh0bDJXdXRYUjhGTlZpMGJaVzlVdVJTMEp5M1FGdXhvaGNzdGZveVNJLzdMb1hrd2E4YmNZbGxXZkhrdW5VY1Y4L2ZqWklLZXIxaVp5cUx4RXRPWDRoWitKbXJGUldjYTBXRHB5WGkzU0todkFJMUtzb050bloiLCJtYWMiOiJkMWE2Y2I5NTFmNTU4M2E5OGJjYjZkNTJmYmRiN2ViMGE4NTYzNGI4M2MxNGFmZGIzY2RhN2M5ZjIxMmNjYmU1IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IkpTMGk1SUZJWkR5VXZjcmRRc2lUY0E9PSIsInZhbHVlIjoiNHZucHpxU0E0QnljeE1mS2RaS09sMzAzcnFmVDRxWGhpUUEvaDRWRC95blAvcW9KdHNsNlNpZ1lYQkxmSU1HUFVPdXhqeVQwNEFHOHQ1cGhpMzVzaEVPUSsvR1pXb2JQbEp4U1ZmdjczcWQ1aDRDY1VhcDJSTEljcS96R3FEOUEiLCJtYWMiOiIwYjY3NWZhYTkxNzRjZDViNmNlNWRlY2Y1Nzc4OGRkZTRmNjNhOWFlNzQ1MGMwMDg5NWY0NjgyNDQyMjY5ZGE0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380303.3762</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380303</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87812372\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-402639142 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yVCdhQsriTuZTEQBdHuoxzYc5BgY1Mo8vp1abE4A</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402639142\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1354583296 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:31:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJ5L1lDTHFTT2k0Z1V2TlZMYXdnNVE9PSIsInZhbHVlIjoiOXNXTW13Y0l3SVJMOUJWODdCckh6aFNLZkp5dWJ3eHhzWnh5Kzd2VWJhbGRORE1SS3IvVDZoOS9KRWpMV0RuUHlMYVVGN1V0T004K1AxaDExUzF0M1B0NmcvQWtDVDJSTWpvU1FWcmZWYWIxNm9IdGdqMDh2MDFFU3QvYnJTb1MiLCJtYWMiOiJmNDViNDA1YTRjZTdmZTJlOTRhNmM3ZDRmOTJhNWEwZmRiMjg2NzNiNTU1N2MxMTQyYWNhYWZjMTJkMDA3YzFkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:31:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IlplOXJMZ0tTcnV5Y3hLdVJNdHIvU3c9PSIsInZhbHVlIjoiNUN5Vi93RkpsTk02cytsQkpMMnhkSWR6UGl3N01NaHEwT24xSGhwR2tNVkd0QVl3TDh2QmJsRTFTQ09KS01BVmppWU5CZ0hJVTNLd3lnWFdBeW5zRGtTQmxKZXhkMGNhdFhsNWZqUHcwM2Z2dXpFRWszQ1lreUxUNnFnZytzbTkiLCJtYWMiOiJmZjRiMTdjZTliNDcxNjAyYTBjYTBjNTkxYTM2OTEzMDM3ODZiODM0MjJjYjEyODYzOTkxZjkxNTE5YTY1MzMwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:31:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJ5L1lDTHFTT2k0Z1V2TlZMYXdnNVE9PSIsInZhbHVlIjoiOXNXTW13Y0l3SVJMOUJWODdCckh6aFNLZkp5dWJ3eHhzWnh5Kzd2VWJhbGRORE1SS3IvVDZoOS9KRWpMV0RuUHlMYVVGN1V0T004K1AxaDExUzF0M1B0NmcvQWtDVDJSTWpvU1FWcmZWYWIxNm9IdGdqMDh2MDFFU3QvYnJTb1MiLCJtYWMiOiJmNDViNDA1YTRjZTdmZTJlOTRhNmM3ZDRmOTJhNWEwZmRiMjg2NzNiNTU1N2MxMTQyYWNhYWZjMTJkMDA3YzFkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:31:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IlplOXJMZ0tTcnV5Y3hLdVJNdHIvU3c9PSIsInZhbHVlIjoiNUN5Vi93RkpsTk02cytsQkpMMnhkSWR6UGl3N01NaHEwT24xSGhwR2tNVkd0QVl3TDh2QmJsRTFTQ09KS01BVmppWU5CZ0hJVTNLd3lnWFdBeW5zRGtTQmxKZXhkMGNhdFhsNWZqUHcwM2Z2dXpFRWszQ1lreUxUNnFnZytzbTkiLCJtYWMiOiJmZjRiMTdjZTliNDcxNjAyYTBjYTBjNTkxYTM2OTEzMDM3ODZiODM0MjJjYjEyODYzOTkxZjkxNTE5YTY1MzMwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:31:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354583296\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2056060316 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93iNf8lXgHejYi94STgmkl28JcAGRw3GeMrcnCmv</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056060316\", {\"maxDepth\":0})</script>\n"}}