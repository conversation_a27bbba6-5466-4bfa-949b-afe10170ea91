{"__meta": {"id": "X922f599fe39dc8755ba7420104bc5f77", "datetime": "2025-07-01 17:37:22", "utime": 1751380642.943622, "method": "POST", "uri": "/livewire/message/utilisateurs", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380642.220398, "end": 1751380642.943656, "duration": 0.7232580184936523, "duration_str": "723ms", "measures": [{"label": "Booting", "start": 1751380642.220398, "relative_start": 0, "end": 1751380642.629793, "relative_end": 1751380642.629793, "duration": 0.4093949794769287, "duration_str": "409ms", "params": [], "collector": null}, {"label": "Application", "start": 1751380642.63125, "relative_start": 0.41085195541381836, "end": 1751380642.94366, "relative_end": 4.0531158447265625e-06, "duration": 0.3124101161956787, "duration_str": "312ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27880760, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.utilisateurs.index (\\resources\\views\\livewire\\utilisateurs\\index.blade.php)", "param_count": 33, "params": ["users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "errors", "_instance", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "showInscriptionModal", "selectedUserForInscription", "newInscription", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/index.blade.php&line=0"}, {"name": "livewire.utilisateurs.liste (\\resources\\views\\livewire\\utilisateurs\\liste.blade.php)", "param_count": 35, "params": ["__env", "app", "errors", "_instance", "users", "roles", "parcours", "mentions", "niveaux", "annees", "totalUsers", "activeUsers", "studentsCount", "staffCount", "livewireLayout", "currentPage", "newUser", "editUser", "mediaItems", "isEtu", "query", "newPasswd", "filterRole", "filterStatus", "perPage", "sortField", "sortDirection", "selectedUsers", "selectAll", "rolePermissions", "showInscriptionModal", "selectedUserForInscription", "newInscription", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/utilisateurs/liste.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.014610000000000001, "accumulated_duration_str": "14.61ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0048200000000000005, "duration_str": "4.82ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 32.991}, {"sql": "select * from `media` where `media`.`id` in (23, 24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "inscriptionimsaa", "start_percent": 32.991, "width_percent": 4.723}, {"sql": "select * from `users` where `users`.`id` = 19 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 186}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:186", "connection": "inscriptionimsaa", "start_percent": 37.714, "width_percent": 5.955}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (19)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 186}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:186", "connection": "inscriptionimsaa", "start_percent": 43.669, "width_percent": 4.928}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (19) and `inscription_students`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 186}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:186", "connection": "inscriptionimsaa", "start_percent": 48.597, "width_percent": 5.065}, {"sql": "select * from `parcours` where `parcours`.`id` in (24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 186}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:186", "connection": "inscriptionimsaa", "start_percent": 53.662, "width_percent": 4.517}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (4) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 186}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:186", "connection": "inscriptionimsaa", "start_percent": 58.179, "width_percent": 10.198}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (6) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 186}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:186", "connection": "inscriptionimsaa", "start_percent": 68.378, "width_percent": 4.449}, {"sql": "select count(*) as aggregate from `users` where (`nom` like '%diarin%' or `prenom` like '%diarin%' or `email` like '%diarin%' or `matricule` like '%diarin%') and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `roles`.`id` = '5') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%diarin%", "%diarin%", "%diarin%", "%diarin%", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 72.827, "width_percent": 10.678}, {"sql": "select `id`, `nom`, `prenom`, `email`, `telephone1`, `photo`, `matricule`, `created_at`, `updated_at` from `users` where (`nom` like '%diarin%' or `prenom` like '%diarin%' or `email` like '%diarin%' or `matricule` like '%diarin%') and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `roles`.`id` = '5') and `users`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["%diarin%", "%diarin%", "%diarin%", "%diarin%", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 83.504, "width_percent": 10.472}, {"sql": "select `roles`.`id`, `roles`.`nom`, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (19, 51)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Utilisateurs.php", "line": 342}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Livewire\\Utilisateurs.php:342", "connection": "inscriptionimsaa", "start_percent": 93.977, "width_percent": 6.023}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 1, "App\\Models\\Niveau": 1, "App\\Models\\Parcour": 1, "App\\Models\\InscriptionStudent": 1, "App\\Models\\Role": 3, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": 2, "App\\Models\\User": 4}, "count": 13}, "livewire": {"data": {"utilisateurs #Cz9Fjq7A30Otr0VSTf6u": "array:5 [\n  \"data\" => array:20 [\n    \"currentPage\" => \"liste\"\n    \"newUser\" => []\n    \"editUser\" => []\n    \"mediaItems\" => <PERSON><PERSON>\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2123\n      #items: array:2 [\n        0 => <PERSON><PERSON>\\MediaLibrary\\MediaCollections\\Models\\Media {#1580\n          #connection: \"mysql\"\n          #table: \"media\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:18 [\n            \"id\" => 23\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"d8931ff8-fdaa-4963-a160-f10d21c1ba16\"\n            \"collection_name\" => \"CV\"\n            \"name\" => \"releve RAZAFISOA (1)\"\n            \"file_name\" => \"releve-RAZAFISOA-(1).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #original: array:18 [\n            \"id\" => 23\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"d8931ff8-fdaa-4963-a160-f10d21c1ba16\"\n            \"collection_name\" => \"CV\"\n            \"name\" => \"releve RAZAFISOA (1)\"\n            \"file_name\" => \"releve-RAZAFISOA-(1).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 1\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"manipulations\" => \"array\"\n            \"custom_properties\" => \"array\"\n            \"generated_conversions\" => \"array\"\n            \"responsive_images\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: array:2 [\n            0 => \"original_url\"\n            1 => \"preview_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: []\n        }\n        1 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#1744\n          #connection: \"mysql\"\n          #table: \"media\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:18 [\n            \"id\" => 24\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"7ce3e5d9-5cd3-42fa-9289-62243e8f3cc2\"\n            \"collection_name\" => \"Diplome\"\n            \"name\" => \"releve RAZAFISOA (2)\"\n            \"file_name\" => \"releve-RAZAFISOA-(2).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 2\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #original: array:18 [\n            \"id\" => 24\n            \"model_type\" => \"App\\Models\\User\"\n            \"model_id\" => 17\n            \"uuid\" => \"7ce3e5d9-5cd3-42fa-9289-62243e8f3cc2\"\n            \"collection_name\" => \"Diplome\"\n            \"name\" => \"releve RAZAFISOA (2)\"\n            \"file_name\" => \"releve-RAZAFISOA-(2).pdf\"\n            \"mime_type\" => \"application/pdf\"\n            \"disk\" => \"private\"\n            \"conversions_disk\" => \"private\"\n            \"size\" => 954249\n            \"manipulations\" => \"[]\"\n            \"custom_properties\" => \"[]\"\n            \"generated_conversions\" => \"[]\"\n            \"responsive_images\" => \"[]\"\n            \"order_column\" => 2\n            \"created_at\" => \"2024-04-26 13:11:01\"\n            \"updated_at\" => \"2024-04-26 13:11:01\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"manipulations\" => \"array\"\n            \"custom_properties\" => \"array\"\n            \"generated_conversions\" => \"array\"\n            \"responsive_images\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: array:2 [\n            0 => \"original_url\"\n            1 => \"preview_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: []\n        }\n      ]\n      #escapeWhenCastingToString: false\n      +collectionName: null\n      +formFieldName: null\n    }\n    \"isEtu\" => false\n    \"query\" => \"diarin\"\n    \"newPasswd\" => null\n    \"filterRole\" => \"5\"\n    \"filterStatus\" => \"\"\n    \"perPage\" => 10\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"selectedUsers\" => []\n    \"selectAll\" => false\n    \"rolePermissions\" => array:2 [\n      \"roles\" => array:7 [\n        0 => array:3 [\n          \"role_id\" => 1\n          \"role_nom\" => \"superadmin\"\n          \"active\" => true\n        ]\n        1 => array:3 [\n          \"role_id\" => 2\n          \"role_nom\" => \"enseignant\"\n          \"active\" => false\n        ]\n        2 => array:3 [\n          \"role_id\" => 3\n          \"role_nom\" => \"deraq\"\n          \"active\" => false\n        ]\n        3 => array:3 [\n          \"role_id\" => 4\n          \"role_nom\" => \"secretaire\"\n          \"active\" => false\n        ]\n        4 => array:3 [\n          \"role_id\" => 5\n          \"role_nom\" => \"etudiant\"\n          \"active\" => false\n        ]\n        5 => array:3 [\n          \"role_id\" => 6\n          \"role_nom\" => \"admin\"\n          \"active\" => false\n        ]\n        6 => array:3 [\n          \"role_id\" => 7\n          \"role_nom\" => \"caf\"\n          \"active\" => false\n        ]\n      ]\n      \"permissions\" => []\n    ]\n    \"showInscriptionModal\" => true\n    \"selectedUserForInscription\" => array:5 [\n      \"id\" => 19\n      \"nom\" => \"ANDRIANARIMANANA\"\n      \"prenom\" => \"Diarintsoa\"\n      \"email\" => \"<EMAIL>\"\n      \"current_inscriptions\" => array:1 [\n        0 => array:5 [\n          \"id\" => 923\n          \"parcour\" => \"ADMINISTRATION DES ETABLISSEMENTS DE SANTE\"\n          \"niveau\" => \"4ème année\"\n          \"annee\" => \"2024/2025\"\n          \"created_at\" => \"30/04/2025\"\n        ]\n      ]\n    ]\n    \"newInscription\" => array:3 [\n      \"annee_universitaire_id\" => \"\"\n      \"niveau_id\" => \"\"\n      \"parcour_id\" => \"\"\n    ]\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"utilisateurs\"\n  \"view\" => \"livewire.utilisateurs.index\"\n  \"component\" => \"App\\Http\\Livewire\\Utilisateurs\"\n  \"id\" => \"Cz9Fjq7A30Otr0VSTf6u\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/payment\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751377855\n]"}, "request": {"path_info": "/livewire/message/utilisateurs", "status_code": "<pre class=sf-dump id=sf-dump-1567986997 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1567986997\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-245783100 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-245783100\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-594372058 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cz9Fjq7A30Otr0VSTf6u</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">utilisateurs</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"26 characters\">habilitations/utilisateurs</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">d726e056</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:20</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>mediaItems</span>\" => []\n      \"<span class=sf-dump-key>isEtu</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"6 characters\">diarin</span>\"\n      \"<span class=sf-dump-key>newPasswd</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filterRole</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>filterStatus</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>selectedUsers</span>\" => []\n      \"<span class=sf-dump-key>selectAll</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>rolePermissions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>roles</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>4</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>6</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>role_nom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n            \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>permissions</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>showInscriptionModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedUserForInscription</span>\" => []\n      \"<span class=sf-dump-key>newInscription</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>annee_universitaire_id</span>\" => \"\"\n        \"<span class=sf-dump-key>niveau_id</span>\" => \"\"\n        \"<span class=sf-dump-key>parcour_id</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mediaItems</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Spatie\\MediaLibrary\\MediaCollections\\Models\\Media</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>23</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>24</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => \"<span class=sf-dump-str title=\"71 characters\">Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">8903ad747daa2dfe792870f25a08da2261224a195a5905de9e059242914e65e4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">587g</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">openInscriptionModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>19</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594372058\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1014233469 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1471</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"74 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?filterRole=5&amp;query=diarin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik0ybzFLbGtxdkxqb2NKczVXOUpGVnc9PSIsInZhbHVlIjoiZE5qNENwbjJjUEVsZzZQekx5WG85Ym8zVnJiaEZjNE9qOUZFNll0NXV3dkZkZ25nYUFxbE9HMCt0MzZvWk80RC9pcmZuTStnc2VUeHlrTVFWZGZHRHhJZkpHWnVNWkZRR3BpQlhhRU5hL2dxTDE3d0dGeGVxSjdyQXpreDFyWXciLCJtYWMiOiIwNTcxM2I2NzcyZDNjM2U3ZjZlZWJjZmNlMTQyMWEyODU1NzVkZWVmODVhMGZhYzg2ZmZmYmM0ODJiNTUxNWUyIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImIyeWE5T2tNMDVBZGhXb0NYVjdzV3c9PSIsInZhbHVlIjoiSEVFVkgxdkFCbmplN0d4RWFvZmhLM2ZxWVpvY0NIN1Nlc01CWGtIV1g3SkFDakdVbG5uRlZibitodGxxenYvRU85K1ZoYTZ0THdHc0NFWlk2V2djZ2JkR1VEckY1TUJyT2Y1QUVOUFg0bWE4MjNRUDBJR0U0S2NWbWxMTG1UMHkiLCJtYWMiOiI3NWJhZWJlNDkzOGY2ZWEzYmJiNjYyZDdmYjQ1OWNkM2NlNDc2MTg4N2ZkY2NlYzQ4ZTE4ODU2YTY4NmExZGEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014233469\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1802811554 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57650</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/index.php/livewire/message/utilisateurs</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1471</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1471</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"74 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?filterRole=5&amp;query=diarin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1258 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik0ybzFLbGtxdkxqb2NKczVXOUpGVnc9PSIsInZhbHVlIjoiZE5qNENwbjJjUEVsZzZQekx5WG85Ym8zVnJiaEZjNE9qOUZFNll0NXV3dkZkZ25nYUFxbE9HMCt0MzZvWk80RC9pcmZuTStnc2VUeHlrTVFWZGZHRHhJZkpHWnVNWkZRR3BpQlhhRU5hL2dxTDE3d0dGeGVxSjdyQXpreDFyWXciLCJtYWMiOiIwNTcxM2I2NzcyZDNjM2U3ZjZlZWJjZmNlMTQyMWEyODU1NzVkZWVmODVhMGZhYzg2ZmZmYmM0ODJiNTUxNWUyIiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6ImIyeWE5T2tNMDVBZGhXb0NYVjdzV3c9PSIsInZhbHVlIjoiSEVFVkgxdkFCbmplN0d4RWFvZmhLM2ZxWVpvY0NIN1Nlc01CWGtIV1g3SkFDakdVbG5uRlZibitodGxxenYvRU85K1ZoYTZ0THdHc0NFWlk2V2djZ2JkR1VEckY1TUJyT2Y1QUVOUFg0bWE4MjNRUDBJR0U0S2NWbWxMTG1UMHkiLCJtYWMiOiI3NWJhZWJlNDkzOGY2ZWEzYmJiNjYyZDdmYjQ1OWNkM2NlNDc2MTg4N2ZkY2NlYzQ4ZTE4ODU2YTY4NmExZGEwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751380642.2204</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751380642</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802811554\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-163467668 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3fVPC4SnbhmIL9JnY36c5Wf3bMPv5WOAm4KXhpdT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163467668\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-23902523 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:37:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxPSElqZk4vZGh6RWVEdHR1SlVrc0E9PSIsInZhbHVlIjoia1FWR1YwTjdEaWoxZ0pyUCtuU3VyYm5Td083YzlETFRGODFnUU5rL1AwREg3dDVoemNYamg5cmIrbkNzbldFMGJmTUU4UlRVaHMrWXRZSi9DMWJ4MEFQMTB4aVRHZm1lMEtSa1oxZHprTTQ3L1pVR3gwR1p3Q0FVL1RyencrRjEiLCJtYWMiOiJlNzU4ZGM4MzQ5OWFlMDQzNGQ4YjQ1YTk2NGE4NGFmZGYxNjA1YTk5YWNmZTc3ZDhlNjQ4ODAwMjQ4NDYxOGFiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:37:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6IkIydTluRmN6K1V6dFZ6ekhQMHNDWnc9PSIsInZhbHVlIjoiZzhwSzFuVElqNGMwVlU2ZDVIRzdZMExWYTlrNmoybFNaZG0zMnRWaXBvM1VsYTZLeENRbnJhS1dzK21sT0VrQjJTM0N4VlpFYnAyK1dEVC95VnRMcjJ0R2FPU3A2Zkh2a0tSeHRhclc0TWZKeHljcTJDRzlrTzZ2V0NLdlh1d3giLCJtYWMiOiJlZDU4M2YxMWYzZmZiMDc4Yjg1OWNiZTNkMmRiOGNkMmUzMTA5YjQ0ZjY0ZGQ5ZDk5YTFlODc2NTk2Yjk4MjczIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:37:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxPSElqZk4vZGh6RWVEdHR1SlVrc0E9PSIsInZhbHVlIjoia1FWR1YwTjdEaWoxZ0pyUCtuU3VyYm5Td083YzlETFRGODFnUU5rL1AwREg3dDVoemNYamg5cmIrbkNzbldFMGJmTUU4UlRVaHMrWXRZSi9DMWJ4MEFQMTB4aVRHZm1lMEtSa1oxZHprTTQ3L1pVR3gwR1p3Q0FVL1RyencrRjEiLCJtYWMiOiJlNzU4ZGM4MzQ5OWFlMDQzNGQ4YjQ1YTk2NGE4NGFmZGYxNjA1YTk5YWNmZTc3ZDhlNjQ4ODAwMjQ4NDYxOGFiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:37:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6IkIydTluRmN6K1V6dFZ6ekhQMHNDWnc9PSIsInZhbHVlIjoiZzhwSzFuVElqNGMwVlU2ZDVIRzdZMExWYTlrNmoybFNaZG0zMnRWaXBvM1VsYTZLeENRbnJhS1dzK21sT0VrQjJTM0N4VlpFYnAyK1dEVC95VnRMcjJ0R2FPU3A2Zkh2a0tSeHRhclc0TWZKeHljcTJDRzlrTzZ2V0NLdlh1d3giLCJtYWMiOiJlZDU4M2YxMWYzZmZiMDc4Yjg1OWNiZTNkMmRiOGNkMmUzMTA5YjQ0ZjY0ZGQ5ZDk5YTFlODc2NTk2Yjk4MjczIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:37:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23902523\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-940091974 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fe9ow7NksmGHr8I98z9kKB0mLyKZar3CkvUxtpug</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/payment</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751377855</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940091974\", {\"maxDepth\":0})</script>\n"}}