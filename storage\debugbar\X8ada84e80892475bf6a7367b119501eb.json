{"__meta": {"id": "X8ada84e80892475bf6a7367b119501eb", "datetime": "2025-07-01 17:57:33", "utime": 1751381853.390497, "method": "POST", "uri": "/livewire/message/multi-step-register", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751381852.916917, "end": 1751381853.390527, "duration": 0.47360992431640625, "duration_str": "474ms", "measures": [{"label": "Booting", "start": 1751381852.916917, "relative_start": 0, "end": 1751381853.228541, "relative_end": 1751381853.228541, "duration": 0.31162381172180176, "duration_str": "312ms", "params": [], "collector": null}, {"label": "Application", "start": 1751381853.229156, "relative_start": 0.3122389316558838, "end": 1751381853.39053, "relative_end": 3.0994415283203125e-06, "duration": 0.16137409210205078, "duration_str": "161ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26458872, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.multi-step-register (\\resources\\views\\livewire\\multi-step-register.blade.php)", "param_count": 17, "params": ["errors", "_instance", "newUser", "niveau", "parcour", "parcours", "cv", "diplome", "releveb<PERSON><PERSON>", "releve1", "releve2", "releve3", "releve4", "terms", "iteration", "currentStep", "previewDocument"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaFoad\\resources\\views/livewire/multi-step-register.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00423, "accumulated_duration_str": "4.23ms", "statements": [{"sql": "select * from `mentions` where `mentions`.`id` in (1, 2, 3, 4, 5, 6, 7, 11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00423, "duration_str": "4.23ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "inscriptionimsaa", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Mention": 9}, "count": 9}, "livewire": {"data": {"multi-step-register #iy2Y5WkfuTzzp2kdh9wt": "array:5 [\n  \"data\" => array:15 [\n    \"newUser\" => array:13 [\n      \"prenom\" => \"York\"\n      \"nom\" => \"Amal\"\n      \"sexe\" => \"F\"\n      \"date_naissance\" => \"1980-02-02\"\n      \"lieu_naissance\" => \"Ut doloribus volupta\"\n      \"adresse\" => \"Dolor ut quaerat aut\"\n      \"nationalite\" => \"Minima quae quo elit\"\n      \"telephone2\" => \"+18245219669\"\n      \"cin\" => \"123412341324\"\n      \"date_delivrance\" => \"2014-04-06\"\n      \"lieu_delivrance\" => \"+****************\"\n      \"email\" => \"<EMAIL>\"\n      \"telephone1\" => \"+2\"\n    ]\n    \"niveau\" => \"1\"\n    \"parcour\" => 1\n    \"parcours\" => Illuminate\\Database\\Eloquent\\Collection {#2057\n      #items: array:9 [\n        0 => App\\Models\\Mention {#1712\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 1\n            \"nom\" => \"Banques Et Assurances\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 1\n            \"nom\" => \"Banques Et Assurances\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Mention {#1710\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 2\n            \"nom\" => \"Commerce International\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Mention {#1709\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 3\n            \"nom\" => \"Finance, Comptabilité et Audit\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 3\n            \"nom\" => \"Finance, Comptabilité et Audit\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Mention {#1708\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 4\n            \"nom\" => \"Tourisme hôtellerie\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 4\n            \"nom\" => \"Tourisme hôtellerie\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Mention {#1707\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Communication et Marketing\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Communication et Marketing\"\n            \"domaine_id\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Mention {#1706\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 6\n            \"nom\" => \"Génie Electrique et Informatique Industrielle\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 6\n            \"nom\" => \"Génie Electrique et Informatique Industrielle\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Mention {#1705\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 7\n            \"nom\" => \"Génie Civil\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 7\n            \"nom\" => \"Génie Civil\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Mention {#1703\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 11\n            \"nom\" => \"Génie Logiciel et Systèmes\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 11\n            \"nom\" => \"Génie Logiciel et Systèmes\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Mention {#1704\n          #connection: \"mysql\"\n          #table: \"mentions\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 8\n            \"nom\" => \"Génie Mécanique\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 8\n            \"nom\" => \"Génie Mécanique\"\n            \"domaine_id\" => 2\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"domaine_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"cv\" => null\n    \"diplome\" => null\n    \"relevebacc\" => null\n    \"releve1\" => null\n    \"releve2\" => null\n    \"releve3\" => null\n    \"releve4\" => null\n    \"terms\" => null\n    \"iteration\" => 2\n    \"currentStep\" => \"informations\"\n    \"previewDocument\" => null\n  ]\n  \"name\" => \"multi-step-register\"\n  \"view\" => \"livewire.multi-step-register\"\n  \"component\" => \"App\\Http\\Livewire\\MultiStepRegister\"\n  \"id\" => \"iy2Y5WkfuTzzp2kdh9wt\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fadT46xXORske6XZ9t0emo4SVvAEHPCtarr5KdaA", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "registration_data": "array:4 [\n  \"newUser\" => array:13 [\n    \"prenom\" => \"York\"\n    \"nom\" => \"Amal\"\n    \"sexe\" => \"F\"\n    \"date_naissance\" => \"1980-02-02\"\n    \"lieu_naissance\" => \"Ut doloribus volupta\"\n    \"adresse\" => \"Dolor ut quaerat aut\"\n    \"nationalite\" => \"Minima quae quo elit\"\n    \"telephone2\" => \"+18245219669\"\n    \"cin\" => \"123412341324\"\n    \"date_delivrance\" => \"2014-04-06\"\n    \"lieu_delivrance\" => \"+****************\"\n    \"email\" => \"<EMAIL>\"\n    \"telephone1\" => \"+2\"\n  ]\n  \"niveau\" => \"1\"\n  \"parcour\" => 1\n  \"currentStep\" => \"informations\"\n]"}, "request": {"path_info": "/livewire/message/multi-step-register", "status_code": "<pre class=sf-dump id=sf-dump-81634298 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-81634298\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1063004071 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1063004071\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-320668888 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">iy2Y5WkfuTzzp2kdh9wt</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">multi-step-register</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newUser.telephone2</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">Format de t&#233;l&#233;phone invalide (ex: +261 XX XX XXX XX)</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>newUser.telephone1</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">Format de t&#233;l&#233;phone invalide (ex: +261 XX XX XXX XX)</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">5d4b2949</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">York</span>\"\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Amal</span>\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n        \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1980-02-02</span>\"\n        \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ut doloribus volupta</span>\"\n        \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolor ut quaerat aut</span>\"\n        \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Minima quae quo elit</span>\"\n        \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+18245219669</span>\"\n        \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341324</span>\"\n        \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2014-04-06</span>\"\n        \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+12278517231</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>parcours</span>\" => []\n      \"<span class=sf-dump-key>cv</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>diplome</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>relevebacc</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve1</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve2</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve3</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>releve4</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>terms</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>iteration</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"12 characters\">informations</span>\"\n      \"<span class=sf-dump-key>previewDocument</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcours</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Mention</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>4</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>5</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>6</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>7</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>11</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>8</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">e1c241d577b6bf2ca9e56c07e23d32c4008d56621bda976b98e4192062fbc5ad</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">t7vq</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">newUser.telephone1</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"2 characters\">+2</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-320668888\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1829335829 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fadT46xXORske6XZ9t0emo4SVvAEHPCtarr5KdaA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ii9rV055TkxyeUFiUHp2TjNJS2o1Y1E9PSIsInZhbHVlIjoicE0zbHAzUk0rNGZEa2ZDNFVLb0RtdVBVaVNaOUgxa0dzcmpMSG40Y0FiSGxMNzkyandEOUtzVGFNcGcySDZxSTF1NEgwaE93dmVxOFpHMk43b2VEK3E3UnB1a1VMb2JOcXQ0ZEs5VUhyMUxVUmoxTjcrZlYvQnBvS01CYW9yVlYiLCJtYWMiOiI1MzkwODgyOTAyYmZhZjU2NzU1OWZmOTBjMDIyMjM3MTQyYTdkNWM1MjEwYjkyYTMyYWVmOWVlODM0OWIyOTc5IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IjlFRVBzdmhndUpEelhXS1NSUWN4MGc9PSIsInZhbHVlIjoiWmtDdkErc1B1bUROekJ3MVRwRjU0LytLQm5PcEdHTXR5cWpGUmM1WWpyeWVDTXhzNWZXUHdUMS9wZVVDSnN4UXJjNGJ1d1dCRzJYZithQXQwUUVFZlY2RmJMQ2lBTGxpWVV2bURGRGgwVnZOQ3VreXVTVTJETGdZeW9PbW8yZnciLCJtYWMiOiJjODU2ZWY3OTZlNDNjYmFjNDU2MTczMjg1YjRlZDZlYjY0ODkwMGFmMjFmZmU0YzI0YzZmMmI4NTQwNjBmODE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829335829\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1733818384 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58900</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\xampp\\htdocs\\ImsaaFoad\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"47 characters\">/index.php/livewire/message/multi-step-register</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1287</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ii9rV055TkxyeUFiUHp2TjNJS2o1Y1E9PSIsInZhbHVlIjoicE0zbHAzUk0rNGZEa2ZDNFVLb0RtdVBVaVNaOUgxa0dzcmpMSG40Y0FiSGxMNzkyandEOUtzVGFNcGcySDZxSTF1NEgwaE93dmVxOFpHMk43b2VEK3E3UnB1a1VMb2JOcXQ0ZEs5VUhyMUxVUmoxTjcrZlYvQnBvS01CYW9yVlYiLCJtYWMiOiI1MzkwODgyOTAyYmZhZjU2NzU1OWZmOTBjMDIyMjM3MTQyYTdkNWM1MjEwYjkyYTMyYWVmOWVlODM0OWIyOTc5IiwidGFnIjoiIn0%3D; foad_imsaa_session=eyJpdiI6IjlFRVBzdmhndUpEelhXS1NSUWN4MGc9PSIsInZhbHVlIjoiWmtDdkErc1B1bUROekJ3MVRwRjU0LytLQm5PcEdHTXR5cWpGUmM1WWpyeWVDTXhzNWZXUHdUMS9wZVVDSnN4UXJjNGJ1d1dCRzJYZithQXQwUUVFZlY2RmJMQ2lBTGxpWVV2bURGRGgwVnZOQ3VreXVTVTJETGdZeW9PbW8yZnciLCJtYWMiOiJjODU2ZWY3OTZlNDNjYmFjNDU2MTczMjg1YjRlZDZlYjY0ODkwMGFmMjFmZmU0YzI0YzZmMmI4NTQwNjBmODE3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751381852.9169</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751381852</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733818384\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-851267477 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fadT46xXORske6XZ9t0emo4SVvAEHPCtarr5KdaA</span>\"\n  \"<span class=sf-dump-key>foad_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T1qPmfuJmwQ5nd6TGuFgKvrTBgXF09ATh7p9J8NS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851267477\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-295849320 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:57:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InVIWjR6UXZZNlFrNnNXNW9QelgyZVE9PSIsInZhbHVlIjoiMXlZTjBzYms5OEd3dkhYMU43UUVWL0dvVk5sZTJDT2l4czhWSjIwYWYvSDhyQUhlL0VMbDM5c2dWY0lSUGUzZDd6QndvTkIrV3pyaGlZZ1dGMG42aFZId3BPZGVzS2tzb1JZYkk2ejc1a2pZaFR6Z0p0ZlBISnVsVG9FeCtEenIiLCJtYWMiOiJmZjAwODM2ZmE1YzZiNzlhYjVmMTJmY2I4MWIwNWJlYzAzZWU5MDZkYmNjMmMyNjM1OTJlOWU3OGMwN2JiOTkwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:57:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">foad_imsaa_session=eyJpdiI6ImMxaS83VytlVFJCaXd6cCt5Q09Jc1E9PSIsInZhbHVlIjoiUE83R3BWb1dkR0xOOC9XNzIyT2xWQ29hQXlNTTVWa0pFcmJzZEY5cnBwZGJFWkhRLyt2VThzanNpeHg5THc2U0JabERIS1FPZndkQWlrVDI5U0h2NEVpUCt6MHFobHlJb2J2dlNSem9VU2xOblZCQmxlakRkRDFyNlJ1MUJjQWoiLCJtYWMiOiJhYWE2OWI2NDJkMGFlMGVmZjQ0YWRhYjcyNTFiNjQ0NDJiYTdiNmNmM2E0MWVkZGEzMDhlODg3OTI0OWU4NmQ3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 16:57:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InVIWjR6UXZZNlFrNnNXNW9QelgyZVE9PSIsInZhbHVlIjoiMXlZTjBzYms5OEd3dkhYMU43UUVWL0dvVk5sZTJDT2l4czhWSjIwYWYvSDhyQUhlL0VMbDM5c2dWY0lSUGUzZDd6QndvTkIrV3pyaGlZZ1dGMG42aFZId3BPZGVzS2tzb1JZYkk2ejc1a2pZaFR6Z0p0ZlBISnVsVG9FeCtEenIiLCJtYWMiOiJmZjAwODM2ZmE1YzZiNzlhYjVmMTJmY2I4MWIwNWJlYzAzZWU5MDZkYmNjMmMyNjM1OTJlOWU3OGMwN2JiOTkwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:57:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">foad_imsaa_session=eyJpdiI6ImMxaS83VytlVFJCaXd6cCt5Q09Jc1E9PSIsInZhbHVlIjoiUE83R3BWb1dkR0xOOC9XNzIyT2xWQ29hQXlNTTVWa0pFcmJzZEY5cnBwZGJFWkhRLyt2VThzanNpeHg5THc2U0JabERIS1FPZndkQWlrVDI5U0h2NEVpUCt6MHFobHlJb2J2dlNSem9VU2xOblZCQmxlakRkRDFyNlJ1MUJjQWoiLCJtYWMiOiJhYWE2OWI2NDJkMGFlMGVmZjQ0YWRhYjcyNTFiNjQ0NDJiYTdiNmNmM2E0MWVkZGEzMDhlODg3OTI0OWU4NmQ3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 16:57:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295849320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1997515141 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fadT46xXORske6XZ9t0emo4SVvAEHPCtarr5KdaA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>registration_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">York</span>\"\n      \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Amal</span>\"\n      \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>F</span>\"\n      \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1980-02-02</span>\"\n      \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ut doloribus volupta</span>\"\n      \"<span class=sf-dump-key>adresse</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolor ut quaerat aut</span>\"\n      \"<span class=sf-dump-key>nationalite</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Minima quae quo elit</span>\"\n      \"<span class=sf-dump-key>telephone2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+18245219669</span>\"\n      \"<span class=sf-dump-key>cin</span>\" => \"<span class=sf-dump-str title=\"12 characters\">123412341324</span>\"\n      \"<span class=sf-dump-key>date_delivrance</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2014-04-06</span>\"\n      \"<span class=sf-dump-key>lieu_delivrance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"2 characters\">+2</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>niveau</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>parcour</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>currentStep</span>\" => \"<span class=sf-dump-str title=\"12 characters\">informations</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997515141\", {\"maxDepth\":0})</script>\n"}}